<!-- Bootstrap 5 Modal Structure -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">
                    <i class="bi bi-key me-2"></i>Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <!-- User Information Card -->
                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-semibold me-3" 
                                 style="width: 40px; height: 40px; background: var(--gradient-primary); font-size: 0.875rem;">
                                <?= strtoupper(substr($user['name'], 0, 1)) ?>
                            </div>
                            <div>
                                <div class="fw-semibold text-dark mb-1">
                                    <?= esc($user['name']) ?>
                                </div>
                                <div class="small text-muted">
                                    @<?= esc($user['username']) ?> • <?= esc($user['email']) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Warning Alert -->
                <div class="alert alert-warning" role="alert">
                    <h6 class="alert-heading">
                        <i class="bi bi-exclamation-triangle me-2"></i>Password Reset Confirmation
                    </h6>
                    <ul class="mb-0 small">
                        <li>This will generate a new 4-digit temporary password</li>
                        <li>The user's current password will be invalidated</li>
                        <li>The new password will be displayed for you to share</li>
                        <li>The user should change this password on next login</li>
                    </ul>
                </div>

                <!-- Confirmation Question -->
                <p class="text-center fw-semibold mb-0">
                    Are you sure you want to reset the password for <strong><?= esc($user['name']) ?></strong>?
                </p>

                <!-- Hidden Form for CSRF -->
                <form id="resetPasswordForm" method="post" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmResetPassword()">
                    <i class="bi bi-key me-1"></i>Reset Password
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap 5 Modal JavaScript Implementation
document.addEventListener('DOMContentLoaded', function() {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    
    if (resetPasswordModal) {
        // Initialize Bootstrap 5 modal
        const modal = new bootstrap.Modal(resetPasswordModal, {
            backdrop: 'static',
            keyboard: true
        });

        // Show the modal automatically when content is loaded
        modal.show();

        // Handle modal events
        resetPasswordModal.addEventListener('show.bs.modal', function (event) {
            // Modal is about to be shown
            console.log('Reset password modal is showing');
        });

        resetPasswordModal.addEventListener('shown.bs.modal', function (event) {
            // Modal is fully shown - focus on the cancel button for accessibility
            const cancelButton = resetPasswordModal.querySelector('[data-bs-dismiss="modal"]');
            if (cancelButton) {
                cancelButton.focus();
            }
        });

        resetPasswordModal.addEventListener('hide.bs.modal', function (event) {
            // Modal is about to be hidden
            console.log('Reset password modal is hiding');
        });

        resetPasswordModal.addEventListener('hidden.bs.modal', function (event) {
            // Modal is fully hidden - clean up if needed
            console.log('Reset password modal is hidden');
        });
    }
});

// Password reset confirmation function
function confirmResetPassword() {
    const form = document.getElementById('resetPasswordForm');
    if (form) {
        form.action = '<?= base_url('admin/users/' . $user['id'] . '/reset-password') ?>';
        form.submit();
    }
}

// Function to hide modal (for backward compatibility)
function hideResetPasswordModal() {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    if (resetPasswordModal) {
        const modal = bootstrap.Modal.getInstance(resetPasswordModal);
        if (modal) {
            modal.hide();
        }
    }
}
</script>
