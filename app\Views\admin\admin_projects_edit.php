<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Projects
</a>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-outline-primary">
    <i class="bi bi-eye me-2"></i>
    View Details
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<?php
// Get validation errors if they exist
$validation = \Config\Services::validation();
$errors = session()->getFlashdata('errors') ?? [];
?>

<!-- Display validation errors if any -->
<?php if (!empty($errors)): ?>
<div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
    <h6 class="alert-heading">
        <i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:
    </h6>
    <ul class="mb-0">
        <?php foreach ($errors as $field => $error): ?>
            <li><?= esc($error) ?></li>
        <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-pencil-square me-2"></i>
            Edit Project
        </h1>
        <p class="text-muted mb-0">
            Update project information for: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-clipboard-data me-2"></i>
            Project Information
        </h5>
    </div>

    <div class="card-body">
        <form action="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" method="post" enctype="multipart/form-data" class="project-edit-form">

            <?= csrf_field() ?>
            
            <!-- Basic Information Section -->
            <div class="mb-5">
                <h4 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-clipboard-data me-2"></i>
                    Basic Information
                </h4>

                <!-- Project Code (Read-only) -->
                <div class="mb-4">
                    <label for="pro_code_display" class="form-label fw-semibold">Project Code</label>
                    <input type="text" id="pro_code_display" class="form-control bg-light text-muted"
                           value="<?= esc($project['pro_code']) ?>" readonly>
                    <div class="form-text">
                        Project code is automatically generated and cannot be changed
                    </div>
                </div>

                <!-- Project Status -->
                <div class="mb-4">
                    <label for="status" class="form-label fw-semibold">
                        Project Status <span class="text-danger">*</span>
                    </label>
                    <select id="status" name="status" class="form-select <?= isset($errors['status']) ? 'is-invalid' : (old('status', $project['status']) ? 'is-valid' : '') ?>" required>
                        <option value="">Select Status</option>
                        <option value="planning" <?= (old('status', $project['status']) === 'planning') ? 'selected' : '' ?>>Planning</option>
                        <option value="active" <?= (old('status', $project['status']) === 'active') ? 'selected' : '' ?>>Active</option>
                        <option value="on-hold" <?= (old('status', $project['status']) === 'on-hold') ? 'selected' : '' ?>>On Hold</option>
                        <option value="completed" <?= (old('status', $project['status']) === 'completed') ? 'selected' : '' ?>>Completed</option>
                        <option value="cancelled" <?= (old('status', $project['status']) === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                    <?php if (isset($errors['status'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['status']) ?></div>
                    <?php endif; ?>
                </div>

                <!-- Project Title -->
                <div class="mb-4">
                    <label for="title" class="form-label fw-semibold">
                        Project Title <span class="text-danger">*</span>
                    </label>
                    <input
                        type="text"
                        id="title"
                        name="title"
                        class="form-control <?= isset($errors['title']) ? 'is-invalid' : (old('title', $project['title']) ? 'is-valid' : '') ?>"
                        value="<?= old('title', $project['title']) ?>"
                        placeholder="Enter project title"
                        required
                        autofocus
                    >
                    <?php if (isset($errors['title'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['title']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Maximum 200 characters
                    </div>
                </div>

                <!-- Other Project IDs -->
                <div class="mb-4">
                    <label for="other_project_ids" class="form-label fw-semibold">Other Project IDs</label>
                    <input
                        type="text"
                        id="other_project_ids"
                        name="other_project_ids"
                        class="form-control <?= isset($errors['other_project_ids']) ? 'is-invalid' : (old('other_project_ids', $project['other_project_ids']) ? 'is-valid' : '') ?>"
                        value="<?= old('other_project_ids', $project['other_project_ids']) ?>"
                        placeholder="External system references (comma-separated)"
                    >
                    <?php if (isset($errors['other_project_ids'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['other_project_ids']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Cross-references to external systems (optional)
                    </div>
                </div>

                <!-- Project Goal -->
                <div class="mb-4">
                    <label for="goal" class="form-label fw-semibold">Project Goal</label>
                    <textarea
                        id="goal"
                        name="goal"
                        class="form-control <?= isset($errors['goal']) ? 'is-invalid' : (old('goal', $project['goal']) ? 'is-valid' : '') ?>"
                        rows="3"
                        placeholder="Describe the main goal of this project"
                    ><?= old('goal', $project['goal']) ?></textarea>
                    <?php if (isset($errors['goal'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['goal']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Brief description of project objectives (max 1000 characters)
                    </div>
                </div>

                <!-- Project Description -->
                <div class="mb-4">
                    <label for="description" class="form-label fw-semibold">Project Description</label>
                    <textarea
                        id="description"
                        name="description"
                        class="form-control <?= isset($errors['description']) ? 'is-invalid' : (old('description', $project['description']) ? 'is-valid' : '') ?>"
                        rows="5"
                        placeholder="Detailed description of the project"
                    ><?= old('description', $project['description']) ?></textarea>
                    <?php if (isset($errors['description'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['description']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Comprehensive project description (optional)
                    </div>
                </div>
            </div>

            <!-- Timeline Section -->
            <div class="mb-5">
                <h4 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-calendar me-2"></i>
                    Timeline & Planning
                </h4>

                <div class="row g-4 mb-4">

                    <!-- Initiation Date -->
                    <div class="col-md-4">
                        <label for="initiation_date" class="form-label fw-semibold">
                            Initiation Date <span class="text-danger">*</span>
                        </label>
                        <input
                            type="date"
                            id="initiation_date"
                            name="initiation_date"
                            class="form-control <?= isset($errors['initiation_date']) ? 'is-invalid' : (old('initiation_date', $project['initiation_date']) ? 'is-valid' : '') ?>"
                            value="<?= old('initiation_date', $project['initiation_date']) ?>"
                            required
                        >
                        <?php if (isset($errors['initiation_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['initiation_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Required field - used for project code generation
                        </div>
                    </div>

                    <!-- Start Date -->
                    <div class="col-md-4">
                        <label for="start_date" class="form-label fw-semibold">Start Date</label>
                        <input
                            type="date"
                            id="start_date"
                            name="start_date"
                            class="form-control <?= isset($errors['start_date']) ? 'is-invalid' : (old('start_date', $project['start_date']) ? 'is-valid' : '') ?>"
                            value="<?= old('start_date', $project['start_date']) ?>"
                        >
                        <?php if (isset($errors['start_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['start_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Project start date (optional)
                        </div>
                    </div>

                    <!-- End Date -->
                    <div class="col-md-4">
                        <label for="end_date" class="form-label fw-semibold">End Date</label>
                        <input
                            type="date"
                            id="end_date"
                            name="end_date"
                            class="form-control <?= isset($errors['end_date']) ? 'is-invalid' : (old('end_date', $project['end_date']) ? 'is-valid' : '') ?>"
                            value="<?= old('end_date', $project['end_date']) ?>"
                        >
                        <?php if (isset($errors['end_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['end_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Project end date (optional)
                        </div>
                    </div>
                </div>

                <div class="row g-4">

                    <!-- Baseline Year -->
                    <div class="col-md-6">
                        <label for="baseline_year" class="form-label fw-semibold">Baseline Year</label>
                        <input
                            type="number"
                            id="baseline_year"
                            name="baseline_year"
                            class="form-control <?= isset($errors['baseline_year']) ? 'is-invalid' : (old('baseline_year', $project['baseline_year']) ? 'is-valid' : '') ?>"
                            value="<?= old('baseline_year', $project['baseline_year']) ?>"
                            min="1900"
                            max="2100"
                            placeholder="e.g., 2024"
                        >
                        <?php if (isset($errors['baseline_year'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['baseline_year']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Baseline measurement year (optional)
                        </div>
                    </div>

                    <!-- Target Year -->
                    <div class="col-md-6">
                        <label for="target_year" class="form-label fw-semibold">Target Year</label>
                        <input
                            type="number"
                            id="target_year"
                            name="target_year"
                            class="form-control <?= isset($errors['target_year']) ? 'is-invalid' : (old('target_year', $project['target_year']) ? 'is-valid' : '') ?>"
                            value="<?= old('target_year', $project['target_year']) ?>"
                            min="1900"
                            max="2100"
                            placeholder="e.g., 2025"
                        >
                        <?php if (isset($errors['target_year'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['target_year']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Target achievement year (optional)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Section -->
            <div class="mb-5">
                <h4 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-geo-alt me-2"></i>
                    Location Information
                </h4>

                <!-- Address Line -->
                <div class="mb-4">
                    <label for="address_line" class="form-label fw-semibold">Address Line</label>
                    <input
                        type="text"
                        id="address_line"
                        name="address_line"
                        class="form-control <?= isset($errors['address_line']) ? 'is-invalid' : (old('address_line', $project['address_line']) ? 'is-valid' : '') ?>"
                        value="<?= old('address_line', $project['address_line']) ?>"
                        placeholder="Street address or location description"
                    >
                    <?php if (isset($errors['address_line'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['address_line']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Street address or location description (optional)
                    </div>
                </div>

                <div class="row g-4 mb-4">

                    <!-- Country -->
                    <div class="col-md-6">
                        <label for="country_id" class="form-label fw-semibold">Country</label>
                        <select id="country_id" name="country_id" class="form-select <?= isset($errors['country_id']) ? 'is-invalid' : (old('country_id', $project['country_id']) ? 'is-valid' : '') ?>">
                            <option value="">Select Country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country['id'] ?>" <?= (old('country_id', $project['country_id']) == $country['id']) ? 'selected' : '' ?>>
                                    <?= esc($country['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['country_id'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['country_id']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select project country (optional)
                        </div>
                    </div>

                    <!-- Province -->
                    <div class="col-md-6">
                        <label for="province_id" class="form-label fw-semibold">Province</label>
                        <select id="province_id" name="province_id" class="form-select <?= isset($errors['province_id']) ? 'is-invalid' : (old('province_id', $project['province_id']) ? 'is-valid' : '') ?>">
                            <option value="">Select Province</option>
                            <?php foreach ($provinces as $province): ?>
                                <option value="<?= $province['id'] ?>"
                                        data-country-id="<?= $province['country_id'] ?>"
                                        <?= (old('province_id', $project['province_id']) == $province['id']) ? 'selected' : '' ?>>
                                    <?= esc($province['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['province_id'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['province_id']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select project province (optional)
                        </div>
                    </div>
                </div>

                <div class="row g-4 mb-4">

                    <!-- District -->
                    <div class="col-md-6">
                        <label for="district_id" class="form-label fw-semibold">District</label>
                        <select id="district_id" name="district_id" class="form-select <?= isset($errors['district_id']) ? 'is-invalid' : (old('district_id', $project['district_id']) ? 'is-valid' : '') ?>">
                            <option value="">Select District</option>
                            <?php foreach ($districts as $district): ?>
                                <option value="<?= $district['id'] ?>"
                                        data-province-id="<?= $district['province_id'] ?>"
                                        <?= (old('district_id', $project['district_id']) == $district['id']) ? 'selected' : '' ?>>
                                    <?= esc($district['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['district_id'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['district_id']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select project district (optional)
                        </div>
                    </div>

                    <!-- LLG -->
                    <div class="col-md-6">
                        <label for="llg_id" class="form-label fw-semibold">Local Level Government</label>
                        <select id="llg_id" name="llg_id" class="form-select <?= isset($errors['llg_id']) ? 'is-invalid' : (old('llg_id', $project['llg_id']) ? 'is-valid' : '') ?>">
                            <option value="">Select LLG</option>
                            <?php foreach ($llgs as $llg): ?>
                                <option value="<?= $llg['id'] ?>"
                                        data-district-id="<?= $llg['district_id'] ?>"
                                        <?= (old('llg_id', $project['llg_id']) == $llg['id']) ? 'selected' : '' ?>>
                                    <?= esc($llg['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['llg_id'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['llg_id']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select Local Level Government (optional)
                        </div>
                    </div>
                </div>

                <div class="row g-4">

                    <!-- Ward Name -->
                    <div class="col-md-4">
                        <label for="ward_name" class="form-label fw-semibold">Ward Name</label>
                        <input
                            type="text"
                            id="ward_name"
                            name="ward_name"
                            class="form-control <?= isset($errors['ward_name']) ? 'is-invalid' : (old('ward_name', $project['ward_name']) ? 'is-valid' : '') ?>"
                            value="<?= old('ward_name', $project['ward_name']) ?>"
                            placeholder="Ward name"
                        >
                        <?php if (isset($errors['ward_name'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['ward_name']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Ward or area name (optional)
                        </div>
                    </div>

                    <!-- Village Name -->
                    <div class="col-md-4">
                        <label for="village_name" class="form-label fw-semibold">Village Name</label>
                        <input
                            type="text"
                            id="village_name"
                            name="village_name"
                            class="form-control <?= isset($errors['village_name']) ? 'is-invalid' : (old('village_name', $project['village_name']) ? 'is-valid' : '') ?>"
                            value="<?= old('village_name', $project['village_name']) ?>"
                            placeholder="Village name"
                        >
                        <?php if (isset($errors['village_name'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['village_name']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Village or community name (optional)
                        </div>
                    </div>

                    <!-- GPS Point -->
                    <div class="col-md-4">
                        <label for="gps_point" class="form-label fw-semibold">GPS Coordinates</label>
                        <input
                            type="text"
                            id="gps_point"
                            name="gps_point"
                            class="form-control <?= isset($errors['gps_point']) ? 'is-invalid' : (old('gps_point', $project['gps_point']) ? 'is-valid' : '') ?>"
                            value="<?= old('gps_point', $project['gps_point']) ?>"
                            placeholder="e.g., -6.2088, 106.8456"
                        >
                        <?php if (isset($errors['gps_point'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['gps_point']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            GPS coordinates for precise location (optional)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files & Documentation Section -->
            <div class="mb-5">
                <h4 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-paperclip me-2"></i>
                    GPS Documentation (Optional)
                </h4>

                <!-- GPS KML File -->
                <div class="mb-4">
                    <label for="gps_kml_file" class="form-label fw-semibold">GPS KML File</label>
                    <?php if ($project['gps_kml_path']): ?>
                        <div class="alert alert-info mb-3">
                            <i class="bi bi-file-earmark me-2"></i>
                            <strong>Current file:</strong> <?= basename($project['gps_kml_path']) ?>
                            <a href="<?= base_url($project['gps_kml_path']) ?>" target="_blank" class="btn btn-outline-primary btn-sm ms-2">
                                <i class="bi bi-download me-1"></i>Download
                            </a>
                        </div>
                    <?php endif; ?>
                    <input
                        type="file"
                        id="gps_kml_file"
                        name="gps_kml_file"
                        class="form-control <?= isset($errors['gps_kml_file']) ? 'is-invalid' : (old('gps_kml_file') ? 'is-valid' : '') ?>"
                        accept=".kml,.kmz"
                    >
                    <?php if (isset($errors['gps_kml_file'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['gps_kml_file']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Optional: Upload KML/KMZ geographic data files (max 25MB). Leave empty to keep current file.
                    </div>
                </div>
            </div>

            <!-- Status & Notes Section -->
            <div class="mb-5">
                <h4 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-sticky me-2"></i>
                    Status & Notes
                </h4>

                <!-- Status Notes -->
                <div class="mb-4">
                    <label for="status_notes" class="form-label fw-semibold">Status Notes</label>
                    <textarea
                        id="status_notes"
                        name="status_notes"
                        class="form-control <?= isset($errors['status_notes']) ? 'is-invalid' : (old('status_notes', $project['status_notes']) ? 'is-valid' : '') ?>"
                        rows="4"
                        placeholder="Additional notes about the current project status"
                    ><?= old('status_notes', $project['status_notes']) ?></textarea>
                    <?php if (isset($errors['status_notes'])): ?>
                        <div class="invalid-feedback"><?= esc($errors['status_notes']) ?></div>
                    <?php endif; ?>
                    <div class="form-text">
                        Optional notes about project status or progress
                    </div>
                </div>
            </div>

            <!-- Information Box -->
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">
                    <i class="bi bi-info-circle me-2"></i>
                    Project Update Notes
                </h6>
                <ul class="mb-0 small">
                    <li>Changes will be saved immediately upon submission</li>
                    <li>Project code is automatically generated (format: PR{NN}{YYYY}) and cannot be changed</li>
                    <li>Initiation date is required and affects project code generation</li>
                    <li>File uploads will replace existing files if new ones are selected</li>
                    <li>Leave file fields empty to keep current files</li>
                    <li>Evaluation documents can be uploaded by evaluators</li>
                    <li>All fields marked with * are required</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3">
                <a href="<?= base_url('admin/projects') ?>" class="btn btn-outline-secondary w-100 w-md-auto">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient w-100 w-md-auto">
                    <i class="bi bi-check-circle me-2"></i>
                    Update Project
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Dependent dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const llgSelect = document.getElementById('llg_id');

    // Store original options
    const originalProvinces = Array.from(provinceSelect.options);
    const originalDistricts = Array.from(districtSelect.options);
    const originalLlgs = Array.from(llgSelect.options);

    function clearSelect(selectElement, placeholder = 'Select...') {
        selectElement.innerHTML = `<option value="">${placeholder}</option>`;
    }

    function filterOptions(selectElement, originalOptions, filterField, filterValue) {
        clearSelect(selectElement);

        if (!filterValue) return;

        originalOptions.forEach(option => {
            if (option.value && option.getAttribute('data-' + filterField) == filterValue) {
                selectElement.appendChild(option.cloneNode(true));
            }
        });
    }

    // Country change handler
    countrySelect.addEventListener('change', function() {
        const countryId = this.value;

        // Clear dependent dropdowns
        clearSelect(provinceSelect, 'Select Province');
        clearSelect(districtSelect, 'Select District');
        clearSelect(llgSelect, 'Select LLG');

        if (countryId) {
            // Filter provinces based on selected country
            filterOptions(provinceSelect, originalProvinces, 'country-id', countryId);
        }
    });

    // Province change handler
    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;

        // Clear dependent dropdowns
        clearSelect(districtSelect, 'Select District');
        clearSelect(llgSelect, 'Select LLG');

        if (provinceId) {
            // Filter districts based on selected province
            filterOptions(districtSelect, originalDistricts, 'province-id', provinceId);
        }
    });

    // District change handler
    districtSelect.addEventListener('change', function() {
        const districtId = this.value;

        // Clear dependent dropdown
        clearSelect(llgSelect, 'Select LLG');

        if (districtId) {
            // Filter LLGs based on selected district
            filterOptions(llgSelect, originalLlgs, 'district-id', districtId);
        }
    });

    // Initialize dropdowns based on current selections (for edit mode)
    if (countrySelect.value) {
        filterOptions(provinceSelect, originalProvinces, 'country-id', countrySelect.value);

        // Wait a bit for provinces to load, then filter districts
        setTimeout(() => {
            if (provinceSelect.value) {
                filterOptions(districtSelect, originalDistricts, 'province-id', provinceSelect.value);

                // Wait a bit for districts to load, then filter LLGs
                setTimeout(() => {
                    if (districtSelect.value) {
                        filterOptions(llgSelect, originalLlgs, 'district-id', districtSelect.value);
                    }
                }, 100);
            }
        }, 100);
    }
});
</script>

<?= $this->endSection() ?>
