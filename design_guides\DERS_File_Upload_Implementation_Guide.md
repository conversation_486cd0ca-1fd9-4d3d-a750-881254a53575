# DERS File Upload Implementation Guide

## Overview
This document provides a comprehensive guide on how file uploads are implemented in the DERS (Dakoii Echad Recruitment & Selection System). It covers the complete process from user file submission to database storage and file viewing.

## Table of Contents
1. [System Architecture](#system-architecture)
2. [File Path Structure](#file-path-structure)
3. [Upload Process Flow](#upload-process-flow)
4. [Implementation Types](#implementation-types)
5. [Database Schema](#database-schema)
6. [Security Considerations](#security-considerations)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## System Architecture

### Directory Structure
```
C:\xampp\htdocs\ders\
├── app/
│   ├── Controllers/
│   ├── Models/
│   └── Views/
├── public/
│   └── uploads/
│       ├── applicants/
│       │   └── {applicant_id}/
│       ├── applications/
│       └── job_descriptions/
└── db_backups/
```

### Key Constants
- **ROOTPATH**: `C:\xampp\htdocs\ders\` (Application root)
- **FCPATH**: `C:\xampp\htdocs\ders\public\` (Public directory)

## File Path Structure

### Database Storage Pattern
All file paths in the database are stored with the `public/` prefix:
```
public/uploads/applicants/{applicant_id}/{filename}
public/uploads/applications/{filename}
public/uploads/job_descriptions/{filename}
```

### Physical File Location
Files are physically stored in:
```
C:\xampp\htdocs\ders\public\uploads\{category}\{filename}
```

### Web Access URL
Files are accessible via:
```
http://localhost/ders/public/uploads/{category}/{filename}
```

### Path Resolution Formula
```php
// Database path: public/uploads/category/filename.pdf
// Physical path: ROOTPATH + database_path
// Web URL: base_url(database_path)
```

## Upload Process Flow

### 1. User Submission
```html
<form enctype="multipart/form-data" method="POST">
    <input type="file" name="file" accept=".pdf">
    <input type="text" name="file_title" required>
    <input type="textarea" name="file_description">
    <button type="submit">Upload</button>
</form>
```

### 2. Server-Side Validation
```php
$validation->setRules([
    'file_title' => 'required|max_length[255]',
    'file_description' => 'permit_empty|max_length[500]',
    'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf]'
]);
```

### 3. File Processing
```php
$file = $this->request->getFile('file');
if ($file && $file->isValid() && !$file->hasMoved()) {
    $newName = $file->getRandomName();
    $uploadPath = FCPATH . 'uploads/category/';
    
    // Create directory if needed
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    // Move file
    $file->move($uploadPath, $newName);
}
```

### 4. Database Storage
```php
$fileData = [
    'application_id' => $applicationId,
    'applicant_id' => $applicant_id,
    'file_title' => $title,
    'file_description' => $description,
    'file_path' => 'public/uploads/category/' . $newName,
    'created_by' => $applicant_id
];
$model->insert($fileData);
```

### 5. File Viewing
```php
// Check file existence
$physicalPath = ROOTPATH . $file['file_path'];
$fileExists = file_exists($physicalPath);

// Generate view URL
$viewUrl = base_url($file['file_path']);
```

## Implementation Types

### 1. Applicant Profile Files
**Location**: `public/uploads/applicants/{applicant_id}/`
**Controller**: `ApplicantController::uploadFile()`
**Model**: `ApplicantFilesModel`
**Table**: `applicant_files`

**Features**:
- AI text extraction with Gemini
- Multiple file types (PDF, DOC, DOCX, images)
- 25MB size limit
- Organized by applicant ID

### 2. Application Files
**Location**: `public/uploads/applications/`
**Controller**: `ApplicantApplicationController::uploadApplicationFile()`
**Model**: `AppxApplicationFilesModel`
**Table**: `appx_application_files`

**Features**:
- PDF only
- 25MB size limit
- Exercise status validation (published only)
- Application ownership verification

### 3. Profile Photos
**Location**: `public/uploads/applicants/{applicant_id}/`
**Controller**: `ApplicantController::uploadPhoto()`
**Model**: `ApplicantsModel`
**Table**: `applicants` (id_photo_path column)

**Features**:
- Image files only (JPG, JPEG, PNG)
- 2MB size limit
- Automatic filename generation

### 4. Job Description Files
**Location**: `public/uploads/job_descriptions/`
**Controller**: `PositionsController`
**Model**: `PositionsModel`
**Table**: `positions` (jd_filepath column)

## Database Schema

### applicant_files
```sql
CREATE TABLE applicant_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_id INT NOT NULL,
    file_title VARCHAR(255) NOT NULL,
    file_description TEXT,
    file_path VARCHAR(255) NOT NULL,
    file_extracted_texts LONGTEXT,
    created_by INT,
    updated_by INT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME
);
```

### appx_application_files
```sql
CREATE TABLE appx_application_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    applicant_id INT NOT NULL,
    applicant_file_id INT,
    file_title VARCHAR(255) NOT NULL,
    file_description TEXT,
    file_path VARCHAR(255) NOT NULL,
    extracted_texts LONGTEXT,
    created_by INT,
    updated_by INT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME
);
```

## Security Considerations

### 1. File Type Validation
```php
'file' => 'ext_in[file,pdf,doc,docx,jpg,jpeg,png]'
```

### 2. Size Limitations
- Profile files: 25MB
- Photos: 2MB
- Enforced at validation level

### 3. Access Control
- Ownership verification before file operations
- Session-based authentication
- Exercise status validation for application files

### 4. Directory Permissions
```php
mkdir($uploadPath, 0755, true);
```

### 5. Unique Filenames
```php
$newName = $file->getRandomName();
// or
$newName = $applicant_id . '_' . time() . '_' . $file->getRandomName();
```

## Best Practices

### 1. Always Use ROOTPATH for File Existence Checks
```php
// CORRECT
$physicalPath = ROOTPATH . $file['file_path'];
$exists = file_exists($physicalPath);

// INCORRECT
$physicalPath = FCPATH . $file['file_path']; // Creates double public/
```

### 2. Store Full Path in Database
```php
// CORRECT - Store with public/ prefix
'file_path' => 'public/uploads/category/' . $newName

// Database path remains unchanged for web access
$webUrl = base_url($file['file_path']);
```

### 3. Error Handling
```php
if ($model->insert($fileData)) {
    return redirect()->with('success', 'File uploaded successfully.');
} else {
    // Clean up uploaded file if database insert fails
    if (file_exists($uploadPath . $newName)) {
        unlink($uploadPath . $newName);
    }
    return redirect()->back()->with('error', 'Failed to save file information.');
}
```

### 4. Directory Creation
```php
if (!is_dir($uploadPath)) {
    mkdir($uploadPath, 0755, true);
}
```

### 5. File Deletion
```php
// Delete physical file
$physicalPath = ROOTPATH . $file['file_path'];
if (file_exists($physicalPath)) {
    unlink($physicalPath);
}

// Delete database record
$model->delete($fileId);
```

## Troubleshooting

### Common Issues

#### 1. Double Public Path Error
**Problem**: `Can't find a route for 'GET: uploads/applications/file.pdf'`
**Cause**: Using `FCPATH . $file['file_path']` creates `public/public/uploads/`
**Solution**: Use `ROOTPATH . $file['file_path']` for file operations

#### 2. File Not Found
**Problem**: File exists but not accessible
**Cause**: Incorrect path resolution
**Solution**: Verify path construction and use correct constants

#### 3. Permission Denied
**Problem**: Cannot create directories or move files
**Cause**: Insufficient permissions
**Solution**: Check directory permissions (755) and web server user

#### 4. Upload Size Exceeded
**Problem**: File upload fails silently
**Cause**: PHP upload limits
**Solution**: Check `upload_max_filesize` and `post_max_size` in php.ini

### Debug Tips

#### 1. Log File Paths
```php
log_message('debug', 'Database path: ' . $file['file_path']);
log_message('debug', 'Physical path: ' . ROOTPATH . $file['file_path']);
log_message('debug', 'File exists: ' . (file_exists(ROOTPATH . $file['file_path']) ? 'Yes' : 'No'));
```

#### 2. Verify Constants
```php
log_message('debug', 'ROOTPATH: ' . ROOTPATH);
log_message('debug', 'FCPATH: ' . FCPATH);
```

#### 3. Check Upload Directory
```php
$uploadPath = FCPATH . 'uploads/category/';
log_message('debug', 'Upload path: ' . $uploadPath);
log_message('debug', 'Directory exists: ' . (is_dir($uploadPath) ? 'Yes' : 'No'));
log_message('debug', 'Directory writable: ' . (is_writable($uploadPath) ? 'Yes' : 'No'));
```

## Conclusion

The DERS file upload system follows a consistent pattern:
1. Files are physically stored in `public/uploads/` subdirectories
2. Database paths include the `public/` prefix
3. Use `ROOTPATH` for file system operations
4. Use `base_url()` for web access
5. Always validate file types, sizes, and user permissions
6. Implement proper error handling and cleanup

This approach ensures security, consistency, and maintainability across the entire application.

---
**Document Version**: 1.0  
**Last Updated**: July 14, 2025  
**Author**: DERS Development Team
