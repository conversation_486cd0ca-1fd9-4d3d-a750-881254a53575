<?php

/**
 * CSRF Helper Functions
 * 
 * Provides convenient functions for CSRF token management across all portals.
 * These functions complement CodeIgniter's built-in CSRF protection with
 * additional functionality for AJAX requests and meta tag generation.
 */

if (!function_exists('csrf_token')) {
    /**
     * Get the current CSRF token value
     * 
     * @return string The current CSRF token
     */
    function csrf_token(): string
    {
        $security = \Config\Services::security();
        return $security->getCSRFHash();
    }
}

if (!function_exists('csrf_token_name')) {
    /**
     * Get the CSRF token name
     * 
     * @return string The CSRF token name
     */
    function csrf_token_name(): string
    {
        $security = \Config\Services::security();
        return $security->getCSRFTokenName();
    }
}

if (!function_exists('csrf_header_name')) {
    /**
     * Get the CSRF header name for AJAX requests
     * 
     * @return string The CSRF header name
     */
    function csrf_header_name(): string
    {
        $security = config('Security');
        return $security->headerName;
    }
}

if (!function_exists('csrf_meta')) {
    /**
     * Generate CSRF meta tags for inclusion in HTML head
     * 
     * @return string HTML meta tags for CSRF token and header name
     */
    function csrf_meta(): string
    {
        $tokenName = csrf_token_name();
        $token = csrf_token();
        $headerName = csrf_header_name();
        
        return '<meta name="csrf-token-name" content="' . esc($tokenName) . '">' . "\n" .
               '<meta name="csrf-token" content="' . esc($token) . '">' . "\n" .
               '<meta name="csrf-header-name" content="' . esc($headerName) . '">';
    }
}

if (!function_exists('csrf_header')) {
    /**
     * Get CSRF header name and value as an array for AJAX requests
     * 
     * @return array Associative array with header name as key and token as value
     */
    function csrf_header(): array
    {
        $headerName = csrf_header_name();
        $token = csrf_token();
        
        return [$headerName => $token];
    }
}

if (!function_exists('csrf_ajax_setup')) {
    /**
     * Generate JavaScript code to set up CSRF protection for AJAX requests
     * 
     * @param bool $includeScriptTags Whether to include <script> tags
     * @return string JavaScript code for CSRF AJAX setup
     */
    function csrf_ajax_setup(bool $includeScriptTags = true): string
    {
        $js = "
// CSRF Protection for AJAX Requests
(function() {
    // Get CSRF token from meta tags
    function getCSRFToken() {
        return document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || '';
    }
    
    // Get CSRF header name from meta tags
    function getCSRFHeaderName() {
        return document.querySelector('meta[name=\"csrf-header-name\"]')?.getAttribute('content') || 'X-CSRF-TOKEN';
    }
    
    // Set up CSRF for jQuery AJAX requests
    if (typeof $ !== 'undefined' && $.ajaxSetup) {
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader(getCSRFHeaderName(), getCSRFToken());
                }
            }
        });
    }
    
    // Set up CSRF for fetch API
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method)) {
            options.headers = options.headers || {};
            options.headers[getCSRFHeaderName()] = getCSRFToken();
        }
        return originalFetch(url, options);
    };
    
    // Refresh CSRF token function
    window.refreshCSRFToken = function() {
        fetch('" . base_url('csrf/refresh') . "', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                [getCSRFHeaderName()]: getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.token) {
                document.querySelector('meta[name=\"csrf-token\"]').setAttribute('content', data.token);
            }
        })
        .catch(error => console.error('CSRF token refresh failed:', error));
    };
})();
";
        
        if ($includeScriptTags) {
            return '<script>' . $js . '</script>';
        }
        
        return $js;
    }
}

if (!function_exists('csrf_form_fields')) {
    /**
     * Generate all necessary CSRF form fields
     * 
     * @return string HTML form fields for CSRF protection
     */
    function csrf_form_fields(): string
    {
        return csrf_field();
    }
}

if (!function_exists('csrf_verify')) {
    /**
     * Manually verify CSRF token (for custom validation scenarios)
     * 
     * @param string|null $token Token to verify (if null, gets from request)
     * @return bool True if token is valid
     */
    function csrf_verify(?string $token = null): bool
    {
        $security = \Config\Services::security();
        $request = \Config\Services::request();
        
        if ($token === null) {
            $token = $request->getPost(csrf_token_name()) ?? 
                    $request->getHeader(csrf_header_name())->getValue() ?? '';
        }
        
        return $security->verifyCSRF($token);
    }
}
