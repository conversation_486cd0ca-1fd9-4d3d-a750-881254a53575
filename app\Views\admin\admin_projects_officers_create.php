<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Officers
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-person-plus me-2"></i>
            Assign Project Officer
        </h1>
        <p class="text-muted mb-0">
            Assign a new officer to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center gap-3">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                 style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                <i class="bi bi-folder"></i>
            </div>
            <div>
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($project['title']) ?>
                </h5>
                <div class="text-muted small">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Officers Summary -->
<?php if (!empty($currentOfficers)): ?>
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>
            Currently Assigned Officers
        </h5>
    </div>

    <div class="card-body">
        <div class="vstack gap-3">
            <?php foreach ($currentOfficers as $officer): ?>
                <div class="bg-light rounded p-3 d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="fw-semibold text-primary">
                            <?= esc($officer['name']) ?>
                        </div>
                        <div class="text-muted small">
                            <?= esc($officer['username']) ?> • <?= esc($officer['email']) ?>
                        </div>
                    </div>
                    <?php
                    $badgeClass = match($officer['role']) {
                        'lead' => 'bg-success',
                        'certifier' => 'bg-info',
                        default => 'bg-secondary'
                    };
                    ?>
                    <span class="badge <?= $badgeClass ?> text-uppercase">
                        <?= esc($officer['role']) ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Assignment Form -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-person-plus me-2"></i>
            Assign New Officer
        </h5>
    </div>
    
    <div class="card-body">
        <?php if (!empty($availableOfficers)): ?>
            <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>">
                <?= csrf_field() ?>
                <!-- Officer Selection -->
                <div class="mb-4">
                    <label for="user_id" class="form-label fw-semibold">
                        Select Officer <span class="text-danger">*</span>
                    </label>
                    <select name="user_id" id="user_id" class="form-select border-danger" required>
                        <option value="">Choose an officer...</option>
                        <?php foreach ($availableOfficers as $officer): ?>
                            <option value="<?= $officer['id'] ?>" <?= old('user_id') == $officer['id'] ? 'selected' : '' ?>>
                                <?= esc($officer['name']) ?> (<?= esc($officer['username']) ?>) - <?= esc($officer['email']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">
                        Only users with Project Officer flag are shown
                    </div>
                </div>

                <!-- Role Selection -->
                <div class="mb-4">
                    <label class="form-label fw-semibold">
                        Officer Role <span class="text-danger">*</span>
                    </label>
                    
                    <div class="vstack gap-3">
                        <!-- Lead Role -->
                        <div class="border rounded p-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="role" value="lead"
                                       id="lead" <?= old('role') == 'lead' ? 'checked' : '' ?> 
                                       <?= !empty($currentOfficers) && array_filter($currentOfficers, fn($o) => $o['role'] === 'lead') ? 'disabled' : '' ?>>
                                <label class="form-check-label" for="lead">
                                    <div class="fw-semibold text-success mb-1">
                                        <i class="bi bi-crown me-2"></i>
                                        Lead Officer
                                    </div>
                                    <div class="text-muted small">
                                        Primary project supervisor with full authority and responsibility. Only one lead per project.
                                    </div>
                                    <?php if (!empty($currentOfficers) && array_filter($currentOfficers, fn($o) => $o['role'] === 'lead')): ?>
                                        <div class="text-warning small mt-1 fw-semibold">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            Lead already assigned to this project
                                        </div>
                                    <?php endif; ?>
                                </label>
                            </div>
                        </div>

                        <!-- Certifier Role -->
                        <div class="border rounded p-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="role" value="certifier"
                                       id="certifier" <?= old('role') == 'certifier' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="certifier">
                                    <div class="fw-semibold text-info mb-1">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Certifier
                                    </div>
                                    <div class="text-muted small">
                                        Responsible for quality assurance, milestone verification, and project certification.
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Support Role -->
                        <div class="border rounded p-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="role" value="support"
                                       id="support" <?= old('role') == 'support' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="support">
                                    <div class="fw-semibold text-secondary mb-1">
                                        <i class="bi bi-person me-2"></i>
                                        Support Officer
                                    </div>
                                    <div class="text-muted small">
                                        Provides assistance and support to the project team with specific tasks and coordination.
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <?php if (empty($currentOfficers)): ?>
                        <div class="alert alert-info mt-3">
                            <div class="fw-semibold">
                                <i class="bi bi-info-circle me-2"></i>
                                First Officer Assignment
                            </div>
                            <div class="small mt-1">
                                This is the first officer being assigned to this project. They will automatically become the Lead Officer regardless of role selection.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Form Actions -->
                <div class="d-flex gap-3 justify-content-end pt-4 border-top">
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary promis-btn-gradient">
                        <i class="bi bi-person-plus me-2"></i>
                        Assign Officer
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 text-muted opacity-50 mb-4">
                    <i class="bi bi-people"></i>
                </div>
                <h5 class="text-muted mb-3">No Available Officers</h5>
                <p class="text-muted mb-4">
                    All project officers in your organization are already assigned to this project, or there are no users with the Project Officer flag.
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                        <i class="bi bi-people me-2"></i>
                        Manage Users
                    </a>
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-primary promis-btn-gradient">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to Officers
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-select lead role if no current officers
<?php if (empty($currentOfficers)): ?>
document.addEventListener('DOMContentLoaded', function() {
    const leadRadio = document.querySelector('input[name="role"][value="lead"]');
    if (leadRadio && !leadRadio.disabled) {
        leadRadio.checked = true;
    }
});
<?php endif; ?>

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const userId = document.querySelector('select[name="user_id"]').value;
    const role = document.querySelector('input[name="role"]:checked');
    
    if (!userId) {
        e.preventDefault();
        alert('Please select an officer to assign.');
        return;
    }
    
    if (!role) {
        e.preventDefault();
        alert('Please select a role for the officer.');
        return;
    }
});
</script>

<?= $this->endSection() ?>
