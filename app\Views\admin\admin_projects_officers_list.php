<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-officers" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-person-plus me-2"></i>
    Assign Officer
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-people me-2"></i>
            Project Officers
        </h1>
        <p class="text-muted mb-0">
            Manage officer assignments and roles for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white me-3 fw-bold"
                 style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                <i class="bi bi-folder"></i>
            </div>
            <div>
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($project['title']) ?>
                </h5>
                <div class="text-muted small">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Officers Summary -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-bar-chart me-2"></i>
            Officers Summary
        </h5>
    </div>

    <div class="card-body">
        <div class="row g-3">
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-primary mb-1">
                        <?= $stats['total_officers'] ?>
                    </div>
                    <div class="text-muted small">Total Officers</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-success mb-1">
                        <?= $stats['by_role']['lead'] ?>
                    </div>
                    <div class="text-muted small">Lead Officers</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-info mb-1">
                        <?= $stats['by_role']['certifier'] ?>
                    </div>
                    <div class="text-muted small">Certifiers</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-secondary mb-1">
                        <?= $stats['by_role']['support'] ?>
                    </div>
                    <div class="text-muted small">Support Officers</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Officers List -->
<div class="card mb-4">
    <div class="card-header bg-light d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>
            Assigned Officers
        </h5>
        <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary promis-btn-gradient btn-sm">
            <i class="bi bi-person-plus me-2"></i>
            Assign New Officer
        </a>
    </div>

    <div class="card-body">
        <?php if (!empty($officers)): ?>
            <div class="vstack gap-4">
                <?php foreach ($officers as $officer): ?>
                    <div class="bg-light rounded p-4 border-start border-4 <?= $officer['role'] === 'lead' ? 'border-success' : ($officer['role'] === 'certifier' ? 'border-info' : 'border-secondary') ?>">
                        <div class="d-flex align-items-start justify-content-between">
                            <!-- Officer Info -->
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center flex-wrap gap-2 mb-3">
                                    <h6 class="fw-semibold text-primary mb-0">
                                        <?= esc($officer['name']) ?>
                                    </h6>
                                    <?php
                                    $badgeClass = match($officer['role']) {
                                        'lead' => 'bg-success',
                                        'certifier' => 'bg-info',
                                        default => 'bg-secondary'
                                    };
                                    $badgeIcon = match($officer['role']) {
                                        'lead' => 'bi-crown',
                                        'certifier' => 'bi-check-circle',
                                        default => 'bi-person'
                                    };
                                    ?>
                                    <span class="badge <?= $badgeClass ?>">
                                        <i class="<?= $badgeIcon ?> me-1"></i>
                                        <?php if ($officer['role'] === 'lead'): ?>
                                            Lead Officer
                                        <?php elseif ($officer['role'] === 'certifier'): ?>
                                            Certifier
                                        <?php else: ?>
                                            Support
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <div class="row g-2 small mb-3">
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Username:</span>
                                        <span class="text-secondary font-monospace"><?= esc($officer['username']) ?></span>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Email:</span>
                                        <span class="text-secondary"><?= esc($officer['email']) ?></span>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Assigned:</span>
                                        <span class="text-secondary"><?= date('M j, Y', strtotime($officer['created_at'])) ?></span>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Status:</span>
                                        <span class="badge bg-success">Active</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Officer Actions -->
                            <div class="d-flex flex-column gap-2">
                                <button onclick="showRemoveOfficerModal(<?= $officer['id'] ?>, '<?= esc($officer['name']) ?>', '<?= esc($officer['role']) ?>')"
                                        class="btn btn-outline-danger btn-sm">
                                    <i class="bi bi-trash me-1"></i>
                                    Remove
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 text-muted opacity-50 mb-4">
                    <i class="bi bi-people"></i>
                </div>
                <h5 class="text-muted mb-3">No Officers Assigned</h5>
                <p class="text-muted mb-4">Start building your project team by assigning the first officer.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-person-plus me-2"></i>
                    Assign First Officer
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Remove Officer Modal -->
<div class="modal fade" id="removeOfficerModal" tabindex="-1" aria-labelledby="removeOfficerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger" id="removeOfficerModalLabel">
                    <i class="bi bi-trash me-2"></i>
                    Remove Project Officer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="removeOfficerForm" method="POST">
                <div class="modal-body">
                    <?= csrf_field() ?>
                    <div class="mb-4">
                        <p class="text-secondary">
                            Are you sure you want to remove <strong id="officerName"></strong> from this project?
                        </p>
                        <p class="text-muted small">
                            Role: <span id="officerRole" class="fw-semibold"></span>
                        </p>
                    </div>

                    <div class="mb-3">
                        <label for="removal_reason" class="form-label fw-semibold">
                            Removal Reason <span class="text-danger">*</span>
                        </label>
                        <textarea name="removal_reason" id="removal_reason" rows="3"
                                  class="form-control border-danger" required
                                  placeholder="Enter reason for removing this officer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>
                        Remove Officer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRemoveOfficerModal(officerId, officerName, officerRole) {
    document.getElementById('officerName').textContent = officerName;
    document.getElementById('officerRole').textContent = officerRole.charAt(0).toUpperCase() + officerRole.slice(1);
    document.getElementById('removeOfficerForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/officers/') ?>' + officerId + '/remove';

    // Show Bootstrap modal
    const modal = new bootstrap.Modal(document.getElementById('removeOfficerModal'));
    modal.show();
}
</script>

<?= $this->endSection() ?>
