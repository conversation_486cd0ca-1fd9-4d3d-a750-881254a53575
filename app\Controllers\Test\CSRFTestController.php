<?php

namespace App\Controllers\Test;

use App\Controllers\BaseController;

/**
 * CSRF Test Controller
 * 
 * Simple controller to test CSRF protection functionality
 * This should only be used in development environment
 */
class CSRFTestController extends BaseController
{
    public function __construct()
    {
        // Only allow in development environment
        if (ENVIRONMENT === 'production') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Page not found');
        }
    }

    /**
     * Display CSRF test page
     */
    public function index()
    {
        $data = [
            'title' => 'CSRF Protection Test - PROMIS',
            'page_title' => 'CSRF Protection Test'
        ];

        return view('test/csrf_test', $data);
    }

    /**
     * Handle form submission test
     */
    public function submitTest()
    {
        // This method will only be reached if CSRF validation passes
        $testInput = $this->request->getPost('test_input');
        
        session()->setFlashdata('success', 'Form submitted successfully! CSRF protection is working. Input: ' . esc($testInput));
        
        return redirect()->to(base_url('test/csrf'));
    }

    /**
     * Handle AJAX submission test
     */
    public function ajaxTest()
    {
        // This method will only be reached if CSRF validation passes
        $input = $this->request->getJSON();
        
        $response = [
            'success' => true,
            'message' => 'AJAX request successful! CSRF protection is working.',
            'data' => $input->test_data ?? 'No data received'
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Get current CSRF token (for debugging)
     */
    public function getToken()
    {
        $response = [
            'token_name' => csrf_token_name(),
            'token' => csrf_token(),
            'header_name' => csrf_header_name()
        ];

        return $this->response->setJSON($response);
    }
}
