# Project Structure & Organization

## CodeIgniter 4 Application Structure

```
app/
├── Controllers/          # MVC Controllers (portal-specific)
│   ├── Admin/           # Admin portal controllers
│   ├── Monitoring/      # Monitor portal controllers
│   ├── Dakoii*.php     # Dakoii portal controllers
│   └── *.php           # Shared/base controllers
├── Models/              # Data models (extend BaseModel for audit)
├── Views/               # Template files organized by portal
│   ├── admin/          # Admin portal views
│   ├── dakoii/         # Dakoii portal views
│   ├── monitoring/     # Monitor portal views
│   ├── templates/      # Portal-specific templates
│   └── emails/         # Email templates
├── Config/              # Framework and app configuration
├── Filters/             # Authentication filters per portal
├── Helpers/             # Utility functions (MapJsonHelper)
├── Libraries/           # Custom services (AuditService)
└── Traits/              # Reusable code (Auditable)
```

## Portal Organization

### Multi-Portal Architecture
- **Dakoii Portal** (`/dakoii`): Super admin interface
- **Admin Portal** (`/admin`): Organization management
- **Monitor Portal** (`/monitor`): Read-only monitoring
- **Landing Pages** (`/`): Public marketing pages

### Naming Conventions

#### Controllers
- **Pattern**: `{Portal}{Feature}Controller.php`
- **Examples**: `DakoiiOrganizationController.php`, `AdminProjectController.php`
- **RESTful Methods**: Separate GET and POST operations (never combine)

#### Models
- **Pattern**: `{Entity}Model.php`
- **Examples**: `OrganizationModel.php`, `ProjectModel.php`
- **Base**: All extend `BaseModel` for automatic audit logging

#### Views
- **Pattern**: `{portal}_{feature}_{action}.php`
- **Examples**: `admin_projects_list.php`, `dakoii_organizations_create.php`
- **Location**: `app/Views/{portal}/`

#### Routes
- **Pattern**: RESTful with portal grouping
- **Examples**: `/admin/projects/create`, `/dakoii/organizations/edit/5`

## File Organization Rules

### Database Structure
- **DO NOT** create new tables or modify existing schema
- **Adapt** Controllers, Views, and Models to existing database
- **Use** existing model relationships and structure

### View Templates
- **Extend** appropriate portal template
- **Dakoii**: `dakoii_portal_template.php` (dark theme)
- **Admin**: `promis_admin_template.php` (light theme)
- **Monitor**: `promis_monitor_template.php` (minimal theme)

### File Uploads
- **Path**: `public/uploads/`
- **Database**: Always store with `public/` prefix
- **Security**: MIME type and size validation (25MB limit)
- **Organization**: `uploads/organizations/{org_id}/{type}/`

## Development Patterns

### Controller Structure
```php
class ExampleController extends BaseController
{
    // GET method for displaying
    public function index() { }
    public function show($id) { }
    public function create() { }
    public function edit($id) { }
    
    // POST method for processing
    public function store() { }
    public function update($id) { }
    public function delete($id) { }
}
```

### Form Handling
- **Standard CodeIgniter 4 form submissions** (NO AJAX)
- **Server-side validation** with redirect on errors
- **Flash messages** for user feedback
- **Bootstrap 5 validation classes** for styling

### Authentication Flow
- **Portal-specific filters**: `DakoiiAuthFilter`, `AdminAuthFilter`
- **Session management**: Portal-prefixed sessions
- **Route protection**: Applied via route groups

## Key Directories

### Public Assets
```
public/
├── uploads/             # User uploaded files
├── system_images/       # System assets
├── map_jsons/          # GeoJSON files for government structure
└── public/             # Static assets (CSS, JS, images)
```

### Configuration
```
.env                     # Environment configuration
app/Config/Routes.php    # Route definitions
app/Config/Filters.php   # Authentication filters
```

### Design Documentation
```
design_guides/           # Project documentation and guides
.kiro/specs/            # Feature specifications
.kiro/steering/         # AI assistant guidance (this file)
```

## Integration Points

### Government Structure
- **GeoJSON files**: `public/map_jsons/`
- **Helper**: `MapJsonHelper` for data loading
- **Hierarchy**: Country → Province → District → LLG

### Audit System
- **Service**: `AuditService` for logging
- **Trait**: `Auditable` for model integration
- **Model**: `AuditLogModel` for data access

### Email System
- **Templates**: `app/Views/emails/`
- **Configuration**: SMTP via `app/Config/Email.php`
- **Features**: User activation, password reset, notifications