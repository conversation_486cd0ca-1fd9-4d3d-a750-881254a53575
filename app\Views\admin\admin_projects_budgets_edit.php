<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Budget List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-currency-dollar me-2"></i>
            Edit Budget Item
        </h1>
        <p class="text-muted mb-0">
            Update budget item for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <h6 class="alert-heading mb-2">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Please correct the following errors:
        </h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <i class="bi bi-exclamation-circle me-2"></i>
        <?= esc(session()->getFlashdata('error')) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        <?= esc(session()->getFlashdata('success')) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Edit Budget Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-wallet me-2"></i>
            Budget Item Information
        </h5>
    </div>

    <div class="card-body">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/budgets/' . $budgetItem['id'] . '/edit') ?>" class="budget-edit-form">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">
                <!-- Item Code -->
                <div class="col-md-6">
                    <label for="item_code" class="form-label fw-semibold">
                        Item Code <span class="text-danger">*</span>
                    </label>
                    <input type="text" id="item_code" name="item_code"
                           class="form-control <?= isset($errors['item_code']) ? 'is-invalid' : '' ?>"
                           value="<?= old('item_code', $budgetItem['item_code']) ?>"
                           placeholder="e.g., BUD001, EQUIP, LABOR" required>
                    <div class="form-text">
                        Unique identifier for this budget item (max 30 characters)
                    </div>
                    <?php if (isset($errors['item_code'])): ?>
                        <div class="invalid-feedback">
                            <?= esc($errors['item_code']) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Category -->
                <div class="col-md-6">
                    <label for="category" class="form-label fw-semibold">Category</label>
                    <select id="category" name="category" class="form-select <?= isset($errors['category']) ? 'is-invalid' : '' ?>">
                        <option value="">Select Category</option>
                        <option value="Personnel" <?= old('category', $budgetItem['category']) == 'Personnel' ? 'selected' : '' ?>>Personnel</option>
                        <option value="Equipment" <?= old('category', $budgetItem['category']) == 'Equipment' ? 'selected' : '' ?>>Equipment</option>
                        <option value="Materials" <?= old('category', $budgetItem['category']) == 'Materials' ? 'selected' : '' ?>>Materials</option>
                        <option value="Services" <?= old('category', $budgetItem['category']) == 'Services' ? 'selected' : '' ?>>Services</option>
                        <option value="Travel" <?= old('category', $budgetItem['category']) == 'Travel' ? 'selected' : '' ?>>Travel</option>
                        <option value="Training" <?= old('category', $budgetItem['category']) == 'Training' ? 'selected' : '' ?>>Training</option>
                        <option value="Other" <?= old('category', $budgetItem['category']) == 'Other' ? 'selected' : '' ?>>Other</option>
                    </select>
                    <div class="form-text">Budget category for reporting purposes</div>
                    <?php if (isset($errors['category'])): ?>
                        <div class="invalid-feedback">
                            <?= esc($errors['category']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mb-4">
                <!-- Description -->
                <label for="description" class="form-label fw-semibold">
                    Description <span class="text-danger">*</span>
                </label>
                <textarea id="description" name="description" rows="3"
                          class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>"
                          placeholder="Detailed description of the budget item..." required><?= old('description', $budgetItem['description']) ?></textarea>
                <div class="form-text">
                    Detailed description of this budget item (max 255 characters)
                </div>
                <?php if (isset($errors['description'])): ?>
                    <div class="invalid-feedback">
                        <?= esc($errors['description']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="row g-4 mb-4">
                <!-- Amount Planned -->
                <div class="col-md-6">
                    <label for="amount_planned" class="form-label fw-semibold">
                        Planned Amount (USD) <span class="text-danger">*</span>
                    </label>
                    <div class="input-group <?= isset($errors['amount_planned']) ? 'has-validation' : '' ?>">
                        <span class="input-group-text">$</span>
                        <input type="number" id="amount_planned" name="amount_planned"
                               class="form-control <?= isset($errors['amount_planned']) ? 'is-invalid' : '' ?>"
                               value="<?= old('amount_planned', $budgetItem['amount_planned']) ?>"
                               placeholder="0.00" step="0.01" min="0.01" required>
                        <?php if (isset($errors['amount_planned'])): ?>
                            <div class="invalid-feedback">
                                <?= esc($errors['amount_planned']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="form-text">
                        Planned budget amount for this item
                    </div>
                </div>

                <!-- Status -->
                <div class="col-md-6">
                    <label for="status" class="form-label fw-semibold">Status</label>
                    <select id="status" name="status" class="form-select <?= isset($errors['status']) ? 'is-invalid' : '' ?>">
                        <option value="active" <?= old('status', $budgetItem['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="removed" <?= old('status', $budgetItem['status']) === 'removed' ? 'selected' : '' ?>>Removed</option>
                    </select>
                    <div class="form-text">
                        Current status of this budget item
                    </div>
                    <?php if (isset($errors['status'])): ?>
                        <div class="invalid-feedback">
                            <?= esc($errors['status']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Audit Information -->
            <div class="card bg-light mb-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-clipboard-data me-2"></i>
                        Audit Information
                    </h6>
                    <div class="small text-muted">
                        <div><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($budgetItem['created_at'])) ?></div>
                        <?php if ($budgetItem['updated_at']): ?>
                            <div><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($budgetItem['updated_at'])) ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex gap-3 justify-content-end pt-4 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-currency-dollar me-2"></i>
                    Update Budget Item
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.querySelector('.budget-edit-form').addEventListener('submit', function(e) {
    const itemCode = document.getElementById('item_code').value.trim();
    const description = document.getElementById('description').value.trim();
    const amountPlanned = document.getElementById('amount_planned').value;

    if (!itemCode || !description || !amountPlanned || parseFloat(amountPlanned) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
