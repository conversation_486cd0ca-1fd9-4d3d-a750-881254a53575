# Technology Stack & Build System

## Framework & Core Technologies

- **Framework**: CodeIgniter 4 (PHP MVC framework)
- **PHP Version**: 8.1+ required
- **Database**: MySQL with MySQLi driver
- **Frontend**: Bootstrap 5 with custom themes per portal
- **Architecture**: Multi-portal MVC with RESTful API patterns

## Development Environment

- **Server**: XAMPP for local development
- **Base URL**: `http://localhost/promis_two/`
- **Database Management**: phpMyAdmin
- **Composer**: Dependency management and autoloading

## Key Libraries & Dependencies

### PHP Dependencies (composer.json)
- `codeigniter4/framework`: ^4.0 (Core framework)
- `fakerphp/faker`: ^1.9 (Development - test data generation)
- `phpunit/phpunit`: ^10.5.16 (Development - testing)

### Frontend Technologies
- **Bootstrap 5**: Component library and responsive grid
- **Custom CSS**: Portal-specific themes (dark glassmorphic for Dakoii, light professional for Admin)
- **JavaScript**: Bootstrap 5 JS bundle, no AJAX for forms (standard form submissions only)

## Common Commands

### Development Setup
```bash
# Install dependencies
composer install

# Update dependencies
composer update

# Run tests
composer test
# or
phpunit
```

### Database Operations
```bash
# Run migrations (if any)
php spark migrate

# Seed database (if seeders exist)
php spark db:seed
```

### CodeIgniter 4 Spark Commands
```bash
# Create controller
php spark make:controller ControllerName

# Create model
php spark make:model ModelName

# Create migration
php spark make:migration MigrationName

# List routes
php spark routes
```

## Build & Deployment

- **No build process required** - PHP interpreted at runtime
- **Asset management**: Static CSS/JS files served from public directory
- **File uploads**: Stored in `public/uploads/` with proper security
- **Environment**: Configure via `.env` file (copy from `env` template)

## Security & Performance

- **Password Hashing**: Argon2 (CodeIgniter 4 default)
- **CSRF Protection**: Available but not globally enabled
- **Session Management**: File-based with configurable timeouts
- **File Upload Security**: MIME type and size validation
- **Audit Trail**: Comprehensive logging via BaseModel extension