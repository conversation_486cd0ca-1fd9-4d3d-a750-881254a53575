<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">CSRF Protection Test</h5>
                </div>
                <div class="card-body">
                    
                    <!-- Flash Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- Test Form with CSRF -->
                        <div class="col-md-6">
                            <h6 class="text-success">✓ Form with CSRF Protection</h6>
                            <form action="<?= base_url('test/csrf-submit') ?>" method="post" class="mb-4">
                                <?= csrf_field() ?>
                                <div class="mb-3">
                                    <label for="test_input_1" class="form-label">Test Input</label>
                                    <input type="text" class="form-control" id="test_input_1" name="test_input" 
                                           value="Test with CSRF" required>
                                </div>
                                <button type="submit" class="btn btn-success">Submit with CSRF</button>
                            </form>
                        </div>

                        <!-- Test Form without CSRF (for demonstration) -->
                        <div class="col-md-6">
                            <h6 class="text-danger">✗ Form without CSRF Protection</h6>
                            <form action="<?= base_url('test/csrf-submit') ?>" method="post" class="mb-4">
                                <!-- No csrf_field() here -->
                                <div class="mb-3">
                                    <label for="test_input_2" class="form-label">Test Input</label>
                                    <input type="text" class="form-control" id="test_input_2" name="test_input" 
                                           value="Test without CSRF" required>
                                </div>
                                <button type="submit" class="btn btn-danger">Submit without CSRF</button>
                            </form>
                        </div>
                    </div>

                    <hr>

                    <!-- CSRF Token Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6>Current CSRF Token Information</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Token Name:</strong></td>
                                        <td><code><?= csrf_token_name() ?></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Token Value:</strong></td>
                                        <td><code><?= csrf_token() ?></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Header Name:</strong></td>
                                        <td><code><?= csrf_header_name() ?></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Meta Tags:</strong></td>
                                        <td>
                                            <small>Check page source for:</small><br>
                                            <code>&lt;meta name="csrf-token" content="..."&gt;</code><br>
                                            <code>&lt;meta name="csrf-header-name" content="..."&gt;</code>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- AJAX Test -->
                    <div class="row">
                        <div class="col-12">
                            <h6>AJAX CSRF Test</h6>
                            <button type="button" class="btn btn-primary" onclick="testAjaxCSRF()">
                                Test AJAX with CSRF
                            </button>
                            <button type="button" class="btn btn-warning" onclick="testAjaxWithoutCSRF()">
                                Test AJAX without CSRF
                            </button>
                            <div id="ajax-result" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAjaxCSRF() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '<div class="alert alert-info">Testing AJAX with CSRF...</div>';
    
    fetch('<?= base_url('test/csrf-ajax') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            test_data: 'AJAX with automatic CSRF'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success">✓ AJAX with CSRF succeeded: ' + data.message + '</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">✗ AJAX with CSRF failed: ' + data.message + '</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger">✗ AJAX Error: ' + error.message + '</div>';
    });
}

function testAjaxWithoutCSRF() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '<div class="alert alert-info">Testing AJAX without CSRF...</div>';
    
    // Override the automatic CSRF injection for this test
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        // Remove CSRF headers for this test
        if (options.headers) {
            delete options.headers['X-CSRF-TOKEN'];
            delete options.headers[getCSRFHeaderName()];
        }
        return originalFetch(url, options);
    };
    
    fetch('<?= base_url('test/csrf-ajax') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            test_data: 'AJAX without CSRF'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success">✓ AJAX without CSRF succeeded: ' + data.message + '</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">✗ AJAX without CSRF failed (expected): ' + data.message + '</div>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '<div class="alert alert-danger">✗ AJAX Error (expected): ' + error.message + '</div>';
    })
    .finally(() => {
        // Restore original fetch
        window.fetch = originalFetch;
    });
}
</script>

<?= $this->endSection() ?>
