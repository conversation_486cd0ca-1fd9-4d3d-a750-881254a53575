<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users/create') ?>" class="btn btn-secondary">
    ← Back to Step 1
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            Create New User
        </h1>
        <p class="text-muted mb-0">
            Step 2 of 2: Roles & Permissions
        </p>
    </div>
</div>

<!-- Progress Indicator -->
<div class="mb-4">
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center fw-semibold" 
                 style="width: 32px; height: 32px; font-size: 0.875rem;">✓</div>
            <span class="ms-2 fw-semibold text-success">Account Details</span>
        </div>
        <div class="flex-fill mx-3">
            <hr class="border-2 border-primary opacity-75">
        </div>
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center fw-semibold" 
                 style="width: 32px; height: 32px; font-size: 0.875rem;">2</div>
            <span class="ms-2 fw-semibold text-primary">Roles & Permissions</span>
        </div>
    </div>
</div>

<!-- Account Summary -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">Account Summary</h5>
    </div>
    <div class="card-body">
        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <h6 class="text-primary fw-semibold mb-1 small">
                    Username
                </h6>
                <p class="text-muted mb-0">
                    <?= esc($step1_data['username']) ?>
                </p>
            </div>
            <div class="col-md-6 col-lg-3">
                <h6 class="text-primary fw-semibold mb-1 small">
                    Email
                </h6>
                <p class="text-muted mb-0">
                    <?= esc($step1_data['email']) ?>
                </p>
            </div>
            <div class="col-md-6 col-lg-3">
                <h6 class="text-primary fw-semibold mb-1 small">
                    Full Name
                </h6>
                <p class="text-muted mb-0">
                    <?= esc($step1_data['name']) ?>
                </p>
            </div>
            <div class="col-md-6 col-lg-3">
                <h6 class="text-primary fw-semibold mb-1 small">
                    Temporary Password
                </h6>
                <p class="text-primary fw-semibold mb-0">
                    <?= esc($temp_password) ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Roles & Permissions Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">Roles & Permissions</h5>
    </div>
    
    <div class="card-body">
        <form action="<?= base_url('admin/users/create/step2/temp') ?>" method="post" class="needs-validation" novalidate>

            <?= csrf_field() ?>
            
            <!-- Organization Assignment (Auto-assigned) -->
            <div class="mb-4">
                <label class="form-label">Organization Assignment</label>
                <div class="alert alert-primary border-primary bg-light d-flex align-items-center">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px; font-size: 1.25rem;">
                        🏢
                    </div>
                    <div>
                        <div class="fw-semibold text-primary mb-1">
                            <?= esc($admin_organization_name) ?>
                        </div>
                        <div class="text-muted small">
                            Users will be automatically assigned to your organization
                        </div>
                    </div>
                </div>
                <div class="form-text">
                    All users created by administrators are automatically assigned to the same organization
                </div>
            </div>

            <!-- Role Selection -->
            <div class="mb-4">
                <label class="form-label">User Role <span class="text-danger">*</span></label>
                <div class="row g-3 mt-1">
                    
                    <!-- Admin Role -->
                    <div class="col-md-6 col-lg-3">
                        <div class="form-check">
                            <input class="form-check-input d-none" type="radio" id="role_admin" name="role" value="admin" <?= (old('role') === 'admin') ? 'checked' : '' ?> required>
                            <label for="role_admin" class="card h-100 border-2 role-card cursor-pointer">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rounded-circle bg-danger text-white d-flex align-items-center justify-content-center me-2" 
                                             style="width: 40px; height: 40px; font-size: 1.125rem;">👑</div>
                                        <h6 class="card-title mb-0 fw-semibold">Administrator</h6>
                                    </div>
                                    <p class="card-text text-muted small mb-0">
                                        Full system access with all permissions. Can manage users, projects, and system settings.
                                    </p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Moderator Role -->
                    <div class="col-md-6 col-lg-3">
                        <div class="form-check">
                            <input class="form-check-input d-none" type="radio" id="role_moderator" name="role" value="moderator" <?= (old('role') === 'moderator') ? 'checked' : '' ?>>
                            <label for="role_moderator" class="card h-100 border-2 role-card cursor-pointer">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rounded-circle bg-warning text-white d-flex align-items-center justify-content-center me-2" 
                                             style="width: 40px; height: 40px; font-size: 1.125rem;">⚖️</div>
                                        <h6 class="card-title mb-0 fw-semibold">Moderator</h6>
                                    </div>
                                    <p class="card-text text-muted small mb-0">
                                        Can manage projects and moderate content. Limited user management capabilities.
                                    </p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Editor Role -->
                    <div class="col-md-6 col-lg-3">
                        <div class="form-check">
                            <input class="form-check-input d-none" type="radio" id="role_editor" name="role" value="editor" <?= (old('role') === 'editor') ? 'checked' : '' ?>>
                            <label for="role_editor" class="card h-100 border-2 role-card cursor-pointer">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" 
                                             style="width: 40px; height: 40px; font-size: 1.125rem;">✏️</div>
                                        <h6 class="card-title mb-0 fw-semibold">Editor</h6>
                                    </div>
                                    <p class="card-text text-muted small mb-0">
                                        Can create and edit projects, documents, and reports. No user management access.
                                    </p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- User Role -->
                    <div class="col-md-6 col-lg-3">
                        <div class="form-check">
                            <input class="form-check-input d-none" type="radio" id="role_user" name="role" value="user" <?= (old('role') === 'user') ? 'checked' : '' ?>>
                            <label for="role_user" class="card h-100 border-2 role-card cursor-pointer">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" 
                                             style="width: 40px; height: 40px; font-size: 1.125rem;">👤</div>
                                        <h6 class="card-title mb-0 fw-semibold">User</h6>
                                    </div>
                                    <p class="card-text text-muted small mb-0">
                                        Basic access to view projects and submit reports. Limited editing capabilities.
                                    </p>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="invalid-feedback">Please select a user role.</div>
            </div>

            <!-- Additional Permissions -->
            <div class="mb-4">
                <label class="form-label">Additional Permissions</label>
                <div class="card border-light bg-light">
                    <div class="card-body">
                        <p class="text-muted small mb-3">
                            Select additional permissions for this user. These can be changed later if needed.
                        </p>

                        <div class="row g-3">

                            <!-- Project Officer Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_project_officer"
                                        name="is_project_officer"
                                        value="1"
                                        <?= old('is_project_officer') ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_project_officer">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    📋 Project Officer
                                                </div>
                                                <div class="text-muted small">
                                                    Can manage project activities and reports
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Evaluator Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_evaluator"
                                        name="is_evaluator"
                                        value="1"
                                        <?= old('is_evaluator') ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_evaluator">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    📊 Evaluator
                                                </div>
                                                <div class="text-muted small">
                                                    Can evaluate projects and submit assessments
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Supervisor Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_supervisor"
                                        name="is_supervisor"
                                        value="1"
                                        <?= old('is_supervisor') ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_supervisor">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    👥 Supervisor
                                                </div>
                                                <div class="text-muted small">
                                                    Can supervise teams and approve activities
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-text">
                    These permissions are in addition to the selected role and can be modified later
                </div>
            </div>

            <!-- Information Box -->
            <div class="alert alert-success border-success bg-light my-4">
                <h6 class="alert-heading text-success fw-semibold mb-2">
                    <i class="bi bi-check-circle me-2"></i>Ready to Create User
                </h6>
                <ul class="text-muted small mb-0 ps-3">
                    <li>User will be activated immediately</li>
                    <li>Organization: <strong><?= esc($admin_organization_name) ?></strong></li>
                    <li>Temporary password: <strong><?= esc($temp_password) ?></strong></li>
                    <li>User should change password on first login</li>
                    <li>You can modify roles and permissions later</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-2 mt-4">
                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-secondary order-2 order-sm-1">
                    ← Back to Step 1
                </a>
                
                <button type="submit" class="btn btn-primary order-1 order-sm-2">
                    Create User Account
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bootstrap 5 Custom Styling -->
<style>
/* Role Card Interactions */
.role-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.role-card:hover {
    border-color: var(--bs-primary) !important;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.form-check-input:checked + .role-card {
    border-color: var(--bs-primary) !important;
    background-color: var(--bs-primary-bg-subtle);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Permission Card Interactions */
.permission-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.permission-card:hover {
    border-color: var(--bs-primary) !important;
    background-color: var(--bs-primary-bg-subtle);
}

.form-check-input:checked + .form-check-label .permission-card {
    border-color: var(--bs-primary) !important;
    background-color: var(--bs-primary-bg-subtle);
}

.form-check-input:checked + .form-check-label .permission-card .fw-semibold {
    color: var(--bs-primary) !important;
}

/* Cursor pointer for clickable labels */
.cursor-pointer {
    cursor: pointer;
}
</style>

<?= $this->endSection() ?>
