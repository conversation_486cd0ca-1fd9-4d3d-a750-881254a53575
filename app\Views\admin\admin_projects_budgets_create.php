<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Budget List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-currency-dollar me-2"></i>
            Create Budget Item
        </h1>
        <p class="text-muted mb-0">
            Add a new budget item to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <h6 class="alert-heading mb-2">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Please correct the following errors:
        </h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <i class="bi bi-exclamation-circle me-2"></i>
        <?= esc(session()->getFlashdata('error')) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        <?= esc(session()->getFlashdata('success')) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Create Budget Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-wallet me-2"></i>
            Budget Item Information
        </h5>
    </div>

    <div class="card-body">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="budget-create-form">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">
                <!-- Left Column -->
                <div class="col-lg-6">
                    <!-- Item Code -->
                    <div class="mb-4">
                        <label for="item_code" class="form-label fw-semibold">
                            Item Code <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="item_code"
                               name="item_code"
                               class="form-control <?= isset($errors['item_code']) ? 'is-invalid' : '' ?>"
                               value="<?= old('item_code') ?>"
                               placeholder="e.g., BUD001, EQUIP, LABOR"
                               required>
                        <div class="form-text">
                            Unique identifier for this budget item (max 30 characters)
                        </div>
                        <?php if (isset($errors['item_code'])): ?>
                            <div class="invalid-feedback">
                                <?= esc($errors['item_code']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                        <label for="description" class="form-label fw-semibold">
                            Description <span class="text-danger">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>"
                                  rows="4"
                                  placeholder="Detailed description of the budget item..."
                                  required><?= old('description') ?></textarea>
                        <div class="form-text">
                            Detailed description of this budget item (max 255 characters)
                        </div>
                        <?php if (isset($errors['description'])): ?>
                            <div class="invalid-feedback">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-lg-6">
                    <!-- Planned Amount -->
                    <div class="mb-4">
                        <label for="amount_planned" class="form-label fw-semibold">
                            Planned Amount <span class="text-danger">*</span>
                        </label>
                        <div class="input-group <?= isset($errors['amount_planned']) ? 'has-validation' : '' ?>">
                            <span class="input-group-text">$</span>
                            <input type="number"
                                   id="amount_planned"
                                   name="amount_planned"
                                   class="form-control <?= isset($errors['amount_planned']) ? 'is-invalid' : '' ?>"
                                   value="<?= old('amount_planned') ?>"
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   required>
                            <?php if (isset($errors['amount_planned'])): ?>
                                <div class="invalid-feedback">
                                    <?= esc($errors['amount_planned']) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="form-text">
                            Planned budget amount for this item
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="form-label fw-semibold">
                            Status
                        </label>
                        <select id="status"
                                name="status"
                                class="form-select <?= isset($errors['status']) ? 'is-invalid' : '' ?>">
                            <option value="active" <?= old('status', 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="removed" <?= old('status') === 'removed' ? 'selected' : '' ?>>Removed</option>
                        </select>
                        <div class="form-text">
                            Current status of this budget item (defaults to Active)
                        </div>
                        <?php if (isset($errors['status'])): ?>
                            <div class="invalid-feedback">
                                <?= esc($errors['status']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex gap-3 justify-content-end pt-4 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-currency-dollar me-2"></i>
                    Create Budget Item
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
document.querySelector('.budget-create-form').addEventListener('submit', function(e) {
    const itemCode = document.getElementById('item_code').value.trim();
    const description = document.getElementById('description').value.trim();
    const amountPlanned = document.getElementById('amount_planned').value;

    if (!itemCode || !description || !amountPlanned || parseFloat(amountPlanned) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
