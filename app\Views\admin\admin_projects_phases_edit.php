<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-phases" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="text-dark fs-2 fw-bold mb-2">
            Edit Phase
        </h1>
        <p class="text-muted mb-0">
            Editing phase: <strong><?= esc($phase['title']) ?></strong> in project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Phase Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-diagram-3 me-2"></i>
            Phase Information
        </h5>
    </div>

    <div class="card-body p-4">
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h6 class="alert-heading">
                    <i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:
                </h6>
                <ul class="mb-0">
                    <?php foreach ($errors as $field => $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/phases/' . $phase['id'] . '/edit') ?>">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">

                <!-- Left Column -->
                <div class="col-md-6">
                    <!-- Phase Code -->
                    <div class="mb-4">
                        <label for="phase_code" class="form-label fw-semibold">
                            Phase Code <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="phase_code"
                               name="phase_code"
                               class="form-control <?= isset($errors['phase_code']) ? 'is-invalid' : '' ?>"
                               value="<?= old('phase_code', $phase['phase_code']) ?>"
                               placeholder="e.g., PH001, INIT, PLAN"
                               required>
                        <?php if (isset($errors['phase_code'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['phase_code']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Unique identifier for this phase (max 20 characters)
                        </div>
                    </div>

                    <!-- Phase Title -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            Phase Title <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="title"
                               name="title"
                               class="form-control <?= isset($errors['title']) ? 'is-invalid' : '' ?>"
                               value="<?= old('title', $phase['title']) ?>"
                               placeholder="e.g., Planning Phase, Implementation Phase"
                               required>
                        <?php if (isset($errors['title'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['title']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Descriptive name for this phase (max 150 characters)
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="form-label fw-semibold">
                            Status <span class="text-danger">*</span>
                        </label>
                        <select id="status"
                                name="status"
                                class="form-select <?= isset($errors['status']) ? 'is-invalid' : '' ?>"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status', $phase['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="deactivated" <?= old('status', $phase['status']) === 'deactivated' ? 'selected' : '' ?>>Deactivated</option>
                        </select>
                        <?php if (isset($errors['status'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['status']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Current status of this phase
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                    <!-- Start Date -->
                    <div class="mb-4">
                        <label for="start_date" class="form-label fw-semibold">
                            Start Date
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               class="form-control <?= isset($errors['start_date']) ? 'is-invalid' : '' ?>"
                               value="<?= old('start_date', $phase['start_date']) ?>">
                        <?php if (isset($errors['start_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['start_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            When this phase is scheduled to start
                        </div>
                    </div>

                    <!-- End Date -->
                    <div class="mb-4">
                        <label for="end_date" class="form-label fw-semibold">
                            End Date
                        </label>
                        <input type="date"
                               id="end_date"
                               name="end_date"
                               class="form-control <?= isset($errors['end_date']) ? 'is-invalid' : '' ?>"
                               value="<?= old('end_date', $phase['end_date']) ?>">
                        <?php if (isset($errors['end_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['end_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            When this phase is scheduled to end
                        </div>
                    </div>

                    <!-- Sort Order -->
                    <div class="mb-4">
                        <label for="sort_order" class="form-label fw-semibold">
                            Sort Order <span class="text-danger">*</span>
                        </label>
                        <input type="number"
                               id="sort_order"
                               name="sort_order"
                               class="form-control <?= isset($errors['sort_order']) ? 'is-invalid' : '' ?>"
                               value="<?= old('sort_order', $phase['sort_order']) ?>"
                               min="1"
                               max="999"
                               required>
                        <?php if (isset($errors['sort_order'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['sort_order']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Display order of this phase (1 = first, higher numbers = later)
                        </div>
                    </div>

                    <!-- Phase Metadata -->
                    <div class="bg-light rounded-3 p-3">
                        <h6 class="text-dark fw-semibold mb-2">
                            <i class="bi bi-info-circle me-2"></i>
                            Phase Metadata
                        </h6>
                        <div class="row g-2 small">
                            <div class="col-5">
                                <span class="text-muted fw-medium">Created:</span>
                            </div>
                            <div class="col-7">
                                <span class="text-secondary"><?= date('M j, Y', strtotime($phase['created_at'])) ?></span>
                            </div>
                            
                            <?php if ($phase['updated_at'] && $phase['updated_at'] !== $phase['created_at']): ?>
                                <div class="col-5">
                                    <span class="text-muted fw-medium">Updated:</span>
                                </div>
                                <div class="col-7">
                                    <span class="text-secondary"><?= date('M j, Y', strtotime($phase['updated_at'])) ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label for="description" class="form-label fw-semibold">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>"
                          rows="4"
                          placeholder="Detailed description of this phase, its objectives, and key activities..."><?= old('description', $phase['description']) ?></textarea>
                <?php if (isset($errors['description'])): ?>
                    <div class="invalid-feedback"><?= esc($errors['description']) ?></div>
                <?php endif; ?>
                <div class="form-text">
                    Optional detailed description of the phase
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-check-circle me-2"></i>
                    Update Phase
                </button>
            </div>
        </form>
    </div>
</div>


<?= $this->endSection() ?>
