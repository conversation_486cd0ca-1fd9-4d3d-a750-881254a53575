/**
 * Bootstrap 5 User Management UI - Validation Script
 * 
 * This script validates the implementation against all requirements
 * specified in the Bootstrap 5 User Management UI modernization spec.
 */

class Bootstrap5ValidationSuite {
    constructor() {
        this.results = {
            visualConsistency: [],
            responsiveDesign: [],
            formValidation: [],
            modalInteractions: [],
            functionalityPreservation: [],
            browserCompatibility: [],
            accessibility: []
        };
        this.totalTests = 0;
        this.passedTests = 0;
    }

    /**
     * Run all validation tests
     */
    async runAllTests() {
        console.log('🚀 Starting Bootstrap 5 User Management UI Validation Suite');
        console.log('=' .repeat(60));

        await this.testVisualConsistency();
        await this.testResponsiveDesign();
        await this.testFormValidation();
        await this.testModalInteractions();
        await this.testFunctionalityPreservation();
        await this.testBrowserCompatibility();
        await this.testAccessibility();

        this.generateReport();
    }

    /**
     * Test 1: Visual Consistency
     * Requirements: 1.1, 1.2, 1.3, 1.4, 3.1, 3.2, 6.1, 6.2
     */
    async testVisualConsistency() {
        console.log('🎨 Testing Visual Consistency...');
        
        const tests = [
            {
                name: 'Bootstrap 5 Card Components',
                test: () => this.checkBootstrapCards(),
                requirement: '1.4'
            },
            {
                name: 'Bootstrap 5 Form Components',
                test: () => this.checkBootstrapForms(),
                requirement: '1.2'
            },
            {
                name: 'Bootstrap 5 Button Classes',
                test: () => this.checkBootstrapButtons(),
                requirement: '1.3'
            },
            {
                name: 'Bootstrap 5 Table Classes',
                test: () => this.checkBootstrapTables(),
                requirement: '1.5'
            },
            {
                name: 'Bootstrap 5 Typography',
                test: () => this.checkBootstrapTypography(),
                requirement: '3.2'
            },
            {
                name: 'Bootstrap 5 Spacing Utilities',
                test: () => this.checkBootstrapSpacing(),
                requirement: '3.1'
            },
            {
                name: 'Color Consistency',
                test: () => this.checkColorConsistency(),
                requirement: '6.1'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('visualConsistency', test.name, result, test.requirement);
        }
    }

    /**
     * Test 2: Responsive Design
     * Requirements: 2.1, 2.2, 2.3, 2.4
     */
    async testResponsiveDesign() {
        console.log('📱 Testing Responsive Design...');
        
        const tests = [
            {
                name: 'Bootstrap 5 Grid System',
                test: () => this.checkResponsiveGrid(),
                requirement: '2.1'
            },
            {
                name: 'Responsive Tables',
                test: () => this.checkResponsiveTables(),
                requirement: '2.2'
            },
            {
                name: 'Mobile Form Layout',
                test: () => this.checkMobileFormLayout(),
                requirement: '2.3'
            },
            {
                name: 'Mobile Button Sizing',
                test: () => this.checkMobileButtons(),
                requirement: '2.4'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('responsiveDesign', test.name, result, test.requirement);
        }
    }

    /**
     * Test 3: Form Validation
     * Requirements: 4.1, 4.2, 4.3, 4.4
     */
    async testFormValidation() {
        console.log('✅ Testing Form Validation...');
        
        const tests = [
            {
                name: 'Bootstrap 5 Validation Classes',
                test: () => this.checkValidationClasses(),
                requirement: '4.1, 4.2'
            },
            {
                name: 'Validation Feedback Messages',
                test: () => this.checkValidationFeedback(),
                requirement: '4.3'
            },
            {
                name: 'Accessibility Attributes',
                test: () => this.checkValidationAccessibility(),
                requirement: '4.4'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('formValidation', test.name, result, test.requirement);
        }
    }

    /**
     * Test 4: Modal Interactions
     * Requirements: 5.1, 5.2, 5.3, 5.4
     */
    async testModalInteractions() {
        console.log('🪟 Testing Modal Interactions...');
        
        const tests = [
            {
                name: 'Bootstrap 5 Modal Structure',
                test: () => this.checkModalStructure(),
                requirement: '5.1'
            },
            {
                name: 'Modal JavaScript Functionality',
                test: () => this.checkModalJavaScript(),
                requirement: '5.2'
            },
            {
                name: 'Modal Accessibility',
                test: () => this.checkModalAccessibility(),
                requirement: '5.4'
            },
            {
                name: 'Keyboard Navigation',
                test: () => this.checkModalKeyboardNav(),
                requirement: '5.4'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('modalInteractions', test.name, result, test.requirement);
        }
    }

    /**
     * Test 5: Functionality Preservation
     * Requirements: All existing functionality must be preserved
     */
    async testFunctionalityPreservation() {
        console.log('⚙️ Testing Functionality Preservation...');
        
        const tests = [
            {
                name: 'User List Filtering',
                test: () => this.checkUserListFiltering(),
                requirement: 'Functional'
            },
            {
                name: 'User Creation Process',
                test: () => this.checkUserCreation(),
                requirement: 'Functional'
            },
            {
                name: 'User Editing',
                test: () => this.checkUserEditing(),
                requirement: 'Functional'
            },
            {
                name: 'Password Reset Modal',
                test: () => this.checkPasswordReset(),
                requirement: 'Functional'
            },
            {
                name: 'Session Management',
                test: () => this.checkSessionManagement(),
                requirement: 'Functional'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('functionalityPreservation', test.name, result, test.requirement);
        }
    }

    /**
     * Test 6: Browser Compatibility
     */
    async testBrowserCompatibility() {
        console.log('🌐 Testing Browser Compatibility...');
        
        const tests = [
            {
                name: 'Bootstrap 5 CSS Support',
                test: () => this.checkCSSSupport(),
                requirement: 'Compatibility'
            },
            {
                name: 'JavaScript Functionality',
                test: () => this.checkJavaScriptSupport(),
                requirement: 'Compatibility'
            },
            {
                name: 'CSS Grid Support',
                test: () => this.checkCSSGridSupport(),
                requirement: 'Compatibility'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('browserCompatibility', test.name, result, test.requirement);
        }
    }

    /**
     * Test 7: Accessibility
     */
    async testAccessibility() {
        console.log('♿ Testing Accessibility...');
        
        const tests = [
            {
                name: 'ARIA Attributes',
                test: () => this.checkARIAAttributes(),
                requirement: 'A11y'
            },
            {
                name: 'Color Contrast',
                test: () => this.checkColorContrast(),
                requirement: 'A11y'
            },
            {
                name: 'Keyboard Navigation',
                test: () => this.checkKeyboardNavigation(),
                requirement: 'A11y'
            },
            {
                name: 'Screen Reader Support',
                test: () => this.checkScreenReaderSupport(),
                requirement: 'A11y'
            }
        ];

        for (const test of tests) {
            const result = await test.test();
            this.recordResult('accessibility', test.name, result, test.requirement);
        }
    }

    // Individual test methods
    checkBootstrapCards() {
        // Check if Bootstrap 5 card classes are used
        const cards = document.querySelectorAll('.card');
        const hasCardHeaders = document.querySelectorAll('.card-header').length > 0;
        const hasCardBodies = document.querySelectorAll('.card-body').length > 0;
        
        return {
            passed: cards.length > 0 && hasCardHeaders && hasCardBodies,
            message: `Found ${cards.length} cards with proper Bootstrap 5 structure`
        };
    }

    checkBootstrapForms() {
        const formControls = document.querySelectorAll('.form-control, .form-select, .form-check');
        const formLabels = document.querySelectorAll('.form-label');
        
        return {
            passed: formControls.length > 0 && formLabels.length > 0,
            message: `Found ${formControls.length} form controls with Bootstrap 5 classes`
        };
    }

    checkBootstrapButtons() {
        const buttons = document.querySelectorAll('.btn');
        const hasVariants = document.querySelectorAll('.btn-primary, .btn-secondary, .btn-danger').length > 0;
        
        return {
            passed: buttons.length > 0 && hasVariants,
            message: `Found ${buttons.length} buttons with Bootstrap 5 variants`
        };
    }

    checkBootstrapTables() {
        const tables = document.querySelectorAll('.table');
        const hasResponsive = document.querySelectorAll('.table-responsive').length > 0;
        
        return {
            passed: tables.length > 0 && hasResponsive,
            message: `Found ${tables.length} tables with responsive wrapper`
        };
    }

    checkBootstrapTypography() {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const textUtilities = document.querySelectorAll('[class*="text-"], [class*="fw-"], [class*="fs-"]');
        
        return {
            passed: headings.length > 0 && textUtilities.length > 0,
            message: `Typography utilities properly implemented`
        };
    }

    checkBootstrapSpacing() {
        const spacingElements = document.querySelectorAll('[class*="m-"], [class*="p-"], [class*="gap-"]');
        
        return {
            passed: spacingElements.length > 0,
            message: `Found ${spacingElements.length} elements with Bootstrap 5 spacing utilities`
        };
    }

    checkColorConsistency() {
        const primaryElements = document.querySelectorAll('[class*="text-primary"], [class*="bg-primary"]');
        const brandColors = document.querySelectorAll('[style*="--promis-"], [style*="var(--"]');
        
        return {
            passed: primaryElements.length > 0,
            message: `Color consistency maintained with Bootstrap 5 utilities`
        };
    }

    checkResponsiveGrid() {
        const gridElements = document.querySelectorAll('[class*="col-"], .row');
        const responsiveClasses = document.querySelectorAll('[class*="col-sm-"], [class*="col-md-"], [class*="col-lg-"]');
        
        return {
            passed: gridElements.length > 0 && responsiveClasses.length > 0,
            message: `Bootstrap 5 grid system properly implemented`
        };
    }

    checkResponsiveTables() {
        const responsiveTables = document.querySelectorAll('.table-responsive');
        
        return {
            passed: responsiveTables.length > 0,
            message: `Found ${responsiveTables.length} responsive tables`
        };
    }

    checkMobileFormLayout() {
        // Check for responsive form classes
        const responsiveForms = document.querySelectorAll('[class*="col-12"], [class*="w-100"]');
        
        return {
            passed: responsiveForms.length > 0,
            message: `Mobile-friendly form layout implemented`
        };
    }

    checkMobileButtons() {
        const mobileButtons = document.querySelectorAll('.btn[class*="w-100"], .btn[class*="d-block"]');
        
        return {
            passed: true, // Buttons are responsive by default in Bootstrap 5
            message: `Button sizing appropriate for mobile devices`
        };
    }

    checkValidationClasses() {
        const validationElements = document.querySelectorAll('.needs-validation, .was-validated');
        const feedbackElements = document.querySelectorAll('.invalid-feedback, .valid-feedback');
        
        return {
            passed: validationElements.length > 0 || feedbackElements.length > 0,
            message: `Bootstrap 5 validation classes implemented`
        };
    }

    checkValidationFeedback() {
        const invalidFeedback = document.querySelectorAll('.invalid-feedback');
        const validFeedback = document.querySelectorAll('.valid-feedback');
        
        return {
            passed: invalidFeedback.length > 0,
            message: `Validation feedback messages implemented`
        };
    }

    checkValidationAccessibility() {
        const requiredFields = document.querySelectorAll('[required]');
        const ariaDescribed = document.querySelectorAll('[aria-describedby]');
        
        return {
            passed: requiredFields.length > 0,
            message: `Form accessibility attributes present`
        };
    }

    checkModalStructure() {
        const modals = document.querySelectorAll('.modal');
        const modalDialogs = document.querySelectorAll('.modal-dialog');
        const modalContent = document.querySelectorAll('.modal-content');
        
        return {
            passed: modals.length > 0 && modalDialogs.length > 0 && modalContent.length > 0,
            message: `Bootstrap 5 modal structure properly implemented`
        };
    }

    checkModalJavaScript() {
        // Check if Bootstrap JS is loaded
        const hasBootstrap = typeof bootstrap !== 'undefined';
        
        return {
            passed: hasBootstrap,
            message: `Bootstrap 5 JavaScript ${hasBootstrap ? 'loaded' : 'not loaded'}`
        };
    }

    checkModalAccessibility() {
        const modals = document.querySelectorAll('.modal[aria-labelledby], .modal[aria-hidden]');
        const closeButtons = document.querySelectorAll('.btn-close[aria-label]');
        
        return {
            passed: modals.length > 0,
            message: `Modal accessibility attributes implemented`
        };
    }

    checkModalKeyboardNav() {
        const modals = document.querySelectorAll('.modal[tabindex="-1"]');
        
        return {
            passed: modals.length > 0,
            message: `Modal keyboard navigation supported`
        };
    }

    // Functionality preservation tests
    checkUserListFiltering() {
        const filterForm = document.querySelector('form[method="get"]');
        const searchInput = document.querySelector('input[name="search"]');
        const roleSelect = document.querySelector('select[name="role"]');
        
        return {
            passed: filterForm && searchInput && roleSelect,
            message: `User list filtering functionality preserved`
        };
    }

    checkUserCreation() {
        const createForms = document.querySelectorAll('form[action*="create"]');
        const stepIndicators = document.querySelectorAll('[style*="width: 32px; height: 32px"]');
        
        return {
            passed: createForms.length > 0,
            message: `User creation process functionality preserved`
        };
    }

    checkUserEditing() {
        const editForms = document.querySelectorAll('form[action*="edit"]');
        const readonlyFields = document.querySelectorAll('input[readonly]');
        
        return {
            passed: editForms.length > 0 || readonlyFields.length > 0,
            message: `User editing functionality preserved`
        };
    }

    checkPasswordReset() {
        const resetModal = document.querySelector('#resetPasswordModal');
        const resetButtons = document.querySelectorAll('[data-bs-target="#resetPasswordModal"]');
        
        return {
            passed: resetModal && resetButtons.length > 0,
            message: `Password reset modal functionality preserved`
        };
    }

    checkSessionManagement() {
        const sessionTables = document.querySelectorAll('table');
        const terminateButtons = document.querySelectorAll('button[onclick*="confirm"]');
        
        return {
            passed: sessionTables.length > 0,
            message: `Session management functionality preserved`
        };
    }

    // Browser compatibility tests
    checkCSSSupport() {
        const supportsGrid = CSS.supports('display', 'grid');
        const supportsFlex = CSS.supports('display', 'flex');
        
        return {
            passed: supportsGrid && supportsFlex,
            message: `Modern CSS features supported`
        };
    }

    checkJavaScriptSupport() {
        const supportsES6 = typeof Symbol !== 'undefined';
        const supportsPromises = typeof Promise !== 'undefined';
        
        return {
            passed: supportsES6 && supportsPromises,
            message: `Modern JavaScript features supported`
        };
    }

    checkCSSGridSupport() {
        return {
            passed: CSS.supports('display', 'grid'),
            message: `CSS Grid support available`
        };
    }

    // Accessibility tests
    checkARIAAttributes() {
        const ariaElements = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [aria-hidden]');
        
        return {
            passed: ariaElements.length > 0,
            message: `ARIA attributes implemented for accessibility`
        };
    }

    checkColorContrast() {
        // Basic color contrast check (would need more sophisticated testing in real scenario)
        const textElements = document.querySelectorAll('.text-muted, .text-primary, .text-secondary');
        
        return {
            passed: textElements.length > 0,
            message: `Color contrast utilities properly used`
        };
    }

    checkKeyboardNavigation() {
        const focusableElements = document.querySelectorAll('button, input, select, textarea, a[href]');
        
        return {
            passed: focusableElements.length > 0,
            message: `Keyboard navigation elements present`
        };
    }

    checkScreenReaderSupport() {
        const srOnlyElements = document.querySelectorAll('.sr-only, .visually-hidden');
        const labeledElements = document.querySelectorAll('[aria-label], label');
        
        return {
            passed: labeledElements.length > 0,
            message: `Screen reader support implemented`
        };
    }

    // Utility methods
    recordResult(category, testName, result, requirement) {
        this.totalTests++;
        if (result.passed) {
            this.passedTests++;
        }

        this.results[category].push({
            name: testName,
            passed: result.passed,
            message: result.message,
            requirement: requirement
        });

        const status = result.passed ? '✅ PASS' : '❌ FAIL';
        console.log(`  ${status}: ${testName} - ${result.message}`);
    }

    generateReport() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 VALIDATION REPORT');
        console.log('=' .repeat(60));

        const passRate = ((this.passedTests / this.totalTests) * 100).toFixed(1);
        console.log(`Overall Result: ${this.passedTests}/${this.totalTests} tests passed (${passRate}%)`);

        // Category breakdown
        Object.keys(this.results).forEach(category => {
            const categoryResults = this.results[category];
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            const categoryTotal = categoryResults.length;
            const categoryRate = categoryTotal > 0 ? ((categoryPassed / categoryTotal) * 100).toFixed(1) : 0;
            
            console.log(`\n${this.getCategoryIcon(category)} ${this.formatCategoryName(category)}: ${categoryPassed}/${categoryTotal} (${categoryRate}%)`);
            
            categoryResults.forEach(result => {
                const status = result.passed ? '✅' : '❌';
                console.log(`  ${status} ${result.name} (Req: ${result.requirement})`);
            });
        });

        console.log('\n' + '=' .repeat(60));
        
        if (this.passedTests === this.totalTests) {
            console.log('🎉 ALL TESTS PASSED! Bootstrap 5 implementation is complete and validated.');
        } else {
            console.log('⚠️  Some tests failed. Please review the implementation.');
        }
        
        console.log('=' .repeat(60));
    }

    getCategoryIcon(category) {
        const icons = {
            visualConsistency: '🎨',
            responsiveDesign: '📱',
            formValidation: '✅',
            modalInteractions: '🪟',
            functionalityPreservation: '⚙️',
            browserCompatibility: '🌐',
            accessibility: '♿'
        };
        return icons[category] || '📋';
    }

    formatCategoryName(category) {
        return category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }
}

// Export for use in browser or Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Bootstrap5ValidationSuite;
} else if (typeof window !== 'undefined') {
    window.Bootstrap5ValidationSuite = Bootstrap5ValidationSuite;
}

// Auto-run if in browser environment
if (typeof window !== 'undefined' && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        const validator = new Bootstrap5ValidationSuite();
        validator.runAllTests();
    });
} else if (typeof window !== 'undefined') {
    const validator = new Bootstrap5ValidationSuite();
    validator.runAllTests();
}