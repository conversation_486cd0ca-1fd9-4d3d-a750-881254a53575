# Design Document

## Overview

This design implements comprehensive Cross-Site Request Forgery (CSRF) protection across all PROMIS portals by enabling global CSRF validation in CodeIgniter 4. The implementation will secure all state-changing HTTP requests (POST, PUT, PATCH, DELETE) while maintaining seamless user experience and integrating with the existing audit trail system.

## Architecture

### Current State Analysis
- CSRF protection is configured but not globally enabled
- Some forms already include `csrf_field()` helper calls
- CSRF filter exists but only applies to POST methods in `$methods` array
- Security configuration is properly set up with cookie-based protection

### Target Architecture
- Global CSRF protection enabled for all state-changing requests
- Automatic token injection in all forms
- Custom error handling with user-friendly messages
- Integration with existing audit trail for security logging
- Environment-specific configuration support

## Components and Interfaces

### 1. Filter Configuration (`app/Config/Filters.php`)

**Current Configuration:**
```php
public array $methods = [
    'POST' => ['csrf'],
];
```

**Enhanced Configuration:**
```php
public array $globals = [
    'before' => [
        'csrf', // Enable globally for all requests
    ],
];

public array $methods = [
    'POST' => ['csrf'],
    'PUT' => ['csrf'],
    'PATCH' => ['csrf'],
    'DELETE' => ['csrf'],
];
```

### 2. Security Configuration (`app/Config/Security.php`)

**Enhanced Settings:**
- Token randomization enabled for better security
- Custom error handling with redirect to previous page
- Configurable token expiration based on environment
- Integration with session management

### 3. Custom CSRF Filter (`app/Filters/CustomCSRFFilter.php`)

**Purpose:** Extend default CSRF filter for custom error handling and logging

**Key Features:**
- Custom error messages per portal
- Integration with audit trail system
- Graceful handling of expired tokens
- Support for AJAX requests

### 4. CSRF Helper Functions (`app/Helpers/CSRFHelper.php`)

**Functions:**
- `csrf_token()` - Get current CSRF token
- `csrf_field()` - Generate hidden form field (already exists)
- `csrf_meta()` - Generate meta tag for AJAX requests
- `csrf_header()` - Get CSRF header name and value

### 5. Template Integration

**Portal Templates Enhancement:**
- Automatic CSRF meta tag injection in all templates
- JavaScript helper for AJAX requests
- Error message display integration

## Data Models

### CSRF Token Management
- **Storage:** Cookie-based (existing configuration)
- **Lifecycle:** 2 hours default expiration
- **Regeneration:** On every form submission
- **Validation:** Server-side validation on all protected requests

### Audit Trail Integration
- **Event Type:** `csrf_validation_failed`
- **Data Captured:**
  - Request URL and method
  - User session information
  - IP address and user agent
  - Timestamp and portal context

## Error Handling

### 1. CSRF Validation Failure Responses

**For Standard Form Submissions:**
- Redirect to previous page with error message
- Preserve form data where possible
- Display user-friendly error explanation

**For AJAX Requests:**
- JSON response with error details
- HTTP 419 status code (CSRF token mismatch)
- Client-side handling instructions

### 2. Custom Error Pages

**Portal-Specific Error Messages:**
- Dakoii Portal: Technical security message
- Admin Portal: Business-friendly explanation
- Monitor Portal: Simple retry instruction

### 3. Token Expiration Handling

**Graceful Degradation:**
- Detect expired tokens before form submission
- Automatic token refresh for long-form sessions
- Clear messaging about session timeouts

## Testing Strategy

### 1. Unit Tests

**CSRF Filter Tests:**
- Valid token acceptance
- Invalid token rejection
- Missing token handling
- Token expiration scenarios

**Helper Function Tests:**
- Token generation consistency
- Form field output validation
- Meta tag generation accuracy

### 2. Integration Tests

**Portal-Specific Tests:**
- Form submission workflows
- AJAX request handling
- Error page rendering
- Audit trail logging

**Cross-Portal Tests:**
- Token isolation between portals
- Session management consistency
- Authentication filter interaction

### 3. Security Tests

**Attack Simulation:**
- Cross-site request forgery attempts
- Token replay attacks
- Session fixation scenarios
- Brute force token guessing

### 4. User Experience Tests

**Form Interaction:**
- Seamless form submissions
- Error message clarity
- Token refresh functionality
- Mobile device compatibility

## Implementation Phases

### Phase 1: Core CSRF Protection
1. Enable global CSRF filter
2. Update security configuration
3. Create custom CSRF filter
4. Implement helper functions

### Phase 2: Template Integration
1. Update portal templates
2. Add CSRF meta tags
3. Implement JavaScript helpers
4. Create error pages

### Phase 3: Audit Integration
1. Extend audit service
2. Add CSRF event logging
3. Create security reports
4. Implement monitoring

### Phase 4: Testing & Validation
1. Comprehensive test suite
2. Security penetration testing
3. User acceptance testing
4. Performance optimization

## Security Considerations

### Token Security
- Cryptographically secure token generation
- Proper token entropy and randomization
- Secure cookie attributes (HttpOnly, Secure, SameSite)
- Token binding to user sessions

### Attack Prevention
- Protection against CSRF attacks
- Prevention of token leakage
- Mitigation of timing attacks
- Defense against token fixation

### Performance Impact
- Minimal overhead for token generation
- Efficient token validation
- Optimized cookie handling
- Reduced server load through proper caching

## Configuration Management

### Environment-Specific Settings

**Development Environment:**
- Longer token expiration for testing
- Detailed error messages
- Debug logging enabled

**Production Environment:**
- Strict token expiration
- Generic error messages
- Security-focused logging

### Portal-Specific Customization
- Different token names per portal
- Customized error handling
- Portal-specific audit requirements

## Monitoring and Maintenance

### Security Metrics
- CSRF attack attempt frequency
- Token validation success rates
- Error response patterns
- User experience impact

### Maintenance Tasks
- Regular security configuration review
- Token expiration optimization
- Error message updates
- Performance monitoring