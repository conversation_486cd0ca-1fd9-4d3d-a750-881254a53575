# Design Document

## Overview

This design document outlines the modernization of the PROMIS Admin User Management interface to fully utilize Bootstrap 5 framework components and utilities. The design maintains the existing functionality and visual hierarchy while replacing custom CSS variables and inline styles with Bootstrap 5 classes for better maintainability and consistency.

## Architecture

### Current State Analysis
- Template already loads Bootstrap 5.3.2 CSS and JavaScript
- Views use mix of Bootstrap classes, custom CSS variables, and inline styles
- Custom CSS variables defined in template (--promis-* variables)
- Inconsistent use of Bootstrap utilities vs custom styling

### Target State
- Full utilization of Bootstrap 5 component library
- Consistent use of Bootstrap 5 utility classes for spacing, typography, and colors
- Maintain custom branding through CSS custom properties where needed
- Responsive design using Bootstrap 5 grid system and responsive utilities

## Components and Interfaces

### 1. User List Page (admin_users_list.php)

#### Current Issues
- Mixed use of inline styles and custom CSS variables
- Table styling uses custom variables instead of Bootstrap classes
- Filter form uses custom styling
- Action buttons use inconsistent styling

#### Design Solution
```html
<!-- Page Header - Bootstrap 5 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-people me-2"></i>User Management
        </h1>
        <p class="text-muted mb-0">Manage user accounts for <strong>Organization Name</strong></p>
    </div>
</div>

<!-- Filters Card - Bootstrap 5 -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0"><i class="bi bi-funnel me-2"></i>Filters & Search</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" placeholder="Search users...">
            </div>
            <div class="col-md-2">
                <label class="form-label">Role</label>
                <select name="role" class="form-select">
                    <option value="">All Roles</option>
                    <!-- options -->
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <!-- options -->
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
            </div>
            <div class="col-md-2">
                <a href="#" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table - Bootstrap 5 -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0"><i class="bi bi-people me-2"></i>Users</h5>
    </div>
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead class="table-light">
                <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- User rows with Bootstrap styling -->
            </tbody>
        </table>
    </div>
</div>
```

### 2. User Creation Forms (admin_users_create_step1.php, admin_users_create_step2.php)

#### Design Solution
```html
<!-- Progress Indicator - Bootstrap 5 -->
<div class="mb-4">
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" 
                 style="width: 32px; height: 32px;">1</div>
            <span class="ms-2 fw-semibold text-primary">Account Details</span>
        </div>
        <div class="flex-fill mx-3">
            <hr class="border-2">
        </div>
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-light text-muted d-flex align-items-center justify-content-center" 
                 style="width: 32px; height: 32px;">2</div>
            <span class="ms-2 text-muted">Roles & Permissions</span>
        </div>
    </div>
</div>

<!-- Form Card - Bootstrap 5 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Account Information</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Username <span class="text-danger">*</span></label>
                    <input type="text" name="username" class="form-control" required>
                    <div class="form-text">Must be unique and at least 3 characters</div>
                    <div class="invalid-feedback">Please provide a valid username.</div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" name="email" class="form-control" required>
                    <div class="invalid-feedback">Please provide a valid email.</div>
                </div>
            </div>
            <!-- Additional form fields -->
        </form>
    </div>
</div>
```

### 3. User Edit Form (admin_users_edit.php)

#### Design Solution
- Similar structure to creation form
- Use Bootstrap 5 form validation classes
- Implement proper disabled states for read-only fields
- Use Bootstrap 5 checkbox and radio button styling for permissions

### 4. Password Reset Modal

#### Design Solution
```html
<!-- Bootstrap 5 Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reset the password for <strong id="resetUserName"></strong>?</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    A new temporary password will be generated and displayed.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmResetPassword()">Reset Password</button>
            </div>
        </div>
    </div>
</div>
```

## Data Models

No changes to existing data models are required. The modernization focuses solely on the presentation layer.

## Error Handling

### Form Validation
- Use Bootstrap 5 validation classes (`is-valid`, `is-invalid`)
- Implement `invalid-feedback` and `valid-feedback` elements
- Use `needs-validation` class on forms for client-side validation
- Maintain server-side validation error display using Bootstrap alert components

### User Feedback
- Flash messages already use Bootstrap 5 alert components
- Maintain existing success/error message functionality
- Ensure proper dismissible behavior with Bootstrap JavaScript

## Testing Strategy

### Visual Regression Testing
1. Compare before/after screenshots of all User Management pages
2. Test responsive behavior across different screen sizes
3. Verify color consistency with existing branding
4. Test interactive elements (hover states, focus states)

### Functional Testing
1. Verify all existing functionality remains intact
2. Test form validation behavior
3. Test modal interactions
4. Test responsive table behavior on mobile devices

### Accessibility Testing
1. Verify proper ARIA attributes on interactive elements
2. Test keyboard navigation
3. Verify color contrast ratios
4. Test screen reader compatibility

### Browser Compatibility
1. Test on modern browsers (Chrome, Firefox, Safari, Edge)
2. Verify Bootstrap 5 compatibility
3. Test responsive behavior across devices

## Implementation Notes

### CSS Custom Properties
- Maintain existing `--promis-*` CSS custom properties for branding
- Use Bootstrap 5 utilities where possible
- Only use custom properties for brand-specific colors and gradients

### JavaScript Dependencies
- Bootstrap 5.3.2 JavaScript is already loaded
- Maintain existing custom JavaScript functionality
- Use Bootstrap 5 JavaScript components for modals, tooltips, etc.

### Responsive Design
- Use Bootstrap 5 grid system (`row`, `col-*` classes)
- Implement responsive utilities (`d-*`, `flex-*` classes)
- Use responsive table classes (`table-responsive`)

### Performance Considerations
- Remove unused custom CSS
- Leverage Bootstrap 5's optimized CSS
- Maintain existing loading performance