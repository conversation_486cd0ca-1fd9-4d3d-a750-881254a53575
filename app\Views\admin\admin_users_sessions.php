<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    <i class="bi bi-arrow-left me-1"></i>Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-shield-lock me-2"></i>Active User Sessions
        </h1>
        <p class="text-muted mb-0">
            Monitor and manage active user sessions
        </p>
    </div>
</div>

<!-- Session Statistics -->
<div class="row g-3 mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h4 fw-bold text-primary mb-1">
                            <?= count($sessions) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Active Sessions
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center fs-4" 
                         style="width: 50px; height: 50px; background: var(--gradient-primary);">
                        <i class="bi bi-shield-lock text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h4 fw-bold text-primary mb-1">
                            <?= count(array_unique(array_column($sessions, 'user_id'))) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Unique Users
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center fs-4" 
                         style="width: 50px; height: 50px; background: var(--gradient-secondary);">
                        <i class="bi bi-people text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h5 fw-bold text-success mb-1">
                            Healthy
                        </h3>
                        <p class="text-muted mb-0 small">
                            System Status
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center fs-4" 
                         style="width: 50px; height: 50px; background: var(--gradient-secondary);">
                        <i class="bi bi-check-circle text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-shield-lock me-2"></i>Active Sessions
        </h5>
    </div>
    
    <?php if (!empty($sessions)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>User</th>
                        <th>Session Info</th>
                        <th>Location</th>
                        <th>Login Time</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sessions as $session): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-3">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-semibold" 
                                         style="width: 40px; height: 40px; background: var(--gradient-primary); font-size: 0.875rem;">
                                        <?= strtoupper(substr($session['username'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-primary mb-1">
                                            <?= esc($session['username']) ?>
                                        </div>
                                        <div class="small text-muted">
                                            ID: <?= esc($session['user_id']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="small text-primary mb-1">
                                        Session: <?= esc(substr($session['session_id'], 0, 8)) ?>...
                                    </div>
                                    <div class="small text-muted">
                                        <?= esc(substr($session['user_agent'], 0, 50)) ?>...
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="small text-secondary">
                                    <?= esc($session['ip_address']) ?>
                                </div>
                                <div class="small text-muted">
                                    <?php
                                    // Simple location detection based on IP
                                    $location = 'Unknown';
                                    if ($session['ip_address'] === '::1' || $session['ip_address'] === '127.0.0.1') {
                                        $location = 'Local Development';
                                    }
                                    echo $location;
                                    ?>
                                </div>
                            </td>
                            <td>
                                <div class="small text-secondary">
                                    <?= date('M j, Y', strtotime($session['login_time'])) ?>
                                </div>
                                <div class="small text-muted">
                                    <?= date('g:i A', strtotime($session['login_time'])) ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge rounded-pill px-2 py-1 small fw-semibold text-uppercase" 
                                      style="background: var(--brand-secondary); color: white;">
                                    <?= esc($session['status']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($session['session_id'] !== session()->session_id): ?>
                                    <form method="post" action="<?= base_url('admin/users/sessions/' . $session['session_id'] . '/terminate') ?>" class="d-inline">
                                        <?= csrf_field() ?>
                                        <button 
                                            type="submit" 
                                            class="btn btn-danger btn-sm"
                                            onclick="return confirm('Are you sure you want to terminate this session?')"
                                            title="Terminate Session"
                                        >
                                            <i class="bi bi-x-circle me-1"></i>Terminate
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span class="text-muted small fst-italic">
                                        Current Session
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center py-5 text-muted">
            <div class="display-1 mb-3">
                <i class="bi bi-shield-lock"></i>
            </div>
            <p class="fs-5 mb-3">No active sessions found</p>
            <p class="mb-0">All users are currently logged out.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Session Management Information -->
<div class="card mt-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-info-circle me-2"></i>Session Management Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="bi bi-clock text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="fw-semibold text-primary mb-2 small">
                            Session Timeout
                        </h6>
                        <p class="text-secondary small mb-0">
                            Sessions automatically expire after 30 minutes of inactivity
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="bi bi-arrow-clockwise text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="fw-semibold text-primary mb-2 small">
                            Auto Refresh
                        </h6>
                        <p class="text-secondary small mb-0">
                            This page refreshes automatically every 30 seconds
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="bi bi-shield-check text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="fw-semibold text-primary mb-2 small">
                            Security
                        </h6>
                        <p class="text-secondary small mb-0">
                            All sessions are tracked and logged for security purposes
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="bi bi-graph-up text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="fw-semibold text-primary mb-2 small">
                            Monitoring
                        </h6>
                        <p class="text-secondary small mb-0">
                            Session data is available in the audit trail
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh script -->
<script>
// Auto-refresh the page every 30 seconds
setTimeout(function() {
    window.location.reload();
}, 30000);

// Show countdown timer
let countdown = 30;
const timer = setInterval(function() {
    countdown--;
    if (countdown <= 0) {
        clearInterval(timer);
    }
}, 1000);
</script>

<?= $this->endSection() ?>
