<?php

namespace App\Filters;

use CodeIgniter\Filters\CSRF;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Config\Services;

/**
 * Custom CSRF Filter
 * 
 * Extends the default CodeIgniter CSRF filter to provide:
 * - Portal-specific error handling
 * - Enhanced logging and audit trail integration
 * - Graceful token expiration handling
 * - Custom error pages per portal
 */
class CustomCSRFFilter extends CSRF
{
    /**
     * Before filter - validates CSRF token with enhanced error handling
     *
     * @param RequestInterface $request
     * @param array|null $arguments
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Skip CSRF validation for GET, HEAD, OPTIONS requests
        if (in_array(strtoupper($request->getMethod()), ['GET', 'HEAD', 'OPTIONS'], true)) {
            return;
        }

        $security = Services::security();
        $tokenName = $security->getCSRFTokenName();
        $headerName = config('Security')->headerName;

        // Get token from POST data or headers
        $token = $request->getPost($tokenName) ?? 
                $request->getHeader($headerName)->getValue() ?? '';

        // Verify CSRF token
        if (!$security->verifyCSRF($token)) {
            return $this->handleCSRFFailure($request);
        }

        return;
    }

    /**
     * Handle CSRF validation failure with portal-specific responses
     *
     * @param RequestInterface $request
     * @return ResponseInterface
     */
    protected function handleCSRFFailure(RequestInterface $request): ResponseInterface
    {
        // Log the CSRF failure for audit trail
        $this->logCSRFFailure($request);

        // Determine portal context
        $portal = $this->detectPortal($request);
        
        // Handle AJAX requests
        if ($request->isAJAX()) {
            return $this->handleAjaxCSRFFailure($request, $portal);
        }

        // Handle regular form submissions
        return $this->handleFormCSRFFailure($request, $portal);
    }

    /**
     * Detect which portal the request is coming from
     *
     * @param RequestInterface $request
     * @return string
     */
    protected function detectPortal(RequestInterface $request): string
    {
        $uri = $request->getUri()->getPath();
        
        if (strpos($uri, '/admin') === 0) {
            return 'admin';
        } elseif (strpos($uri, '/monitor') === 0) {
            return 'monitor';
        } elseif (strpos($uri, '/dakoii') === 0) {
            return 'dakoii';
        }
        
        return 'default';
    }

    /**
     * Handle CSRF failure for AJAX requests
     *
     * @param RequestInterface $request
     * @param string $portal
     * @return ResponseInterface
     */
    protected function handleAjaxCSRFFailure(RequestInterface $request, string $portal): ResponseInterface
    {
        $response = Services::response();
        
        $errorMessages = [
            'admin' => 'Security token expired. Please refresh the page and try again.',
            'monitor' => 'Session expired. Please refresh the page to continue.',
            'dakoii' => 'Security validation failed. Please refresh the page and retry.',
            'default' => 'CSRF token mismatch. Please refresh the page.'
        ];

        $data = [
            'error' => true,
            'message' => $errorMessages[$portal] ?? $errorMessages['default'],
            'code' => 'CSRF_TOKEN_MISMATCH',
            'portal' => $portal,
            'action' => 'refresh_required'
        ];

        return $response->setStatusCode(419)
                       ->setContentType('application/json')
                       ->setBody(json_encode($data));
    }

    /**
     * Handle CSRF failure for regular form submissions
     *
     * @param RequestInterface $request
     * @param string $portal
     * @return ResponseInterface
     */
    protected function handleFormCSRFFailure(RequestInterface $request, string $portal): ResponseInterface
    {
        $response = Services::response();
        $session = Services::session();

        // Set portal-specific error messages
        $errorMessages = [
            'admin' => 'Security token has expired. Your form submission could not be processed for security reasons. Please try again.',
            'monitor' => 'Your session has expired. Please refresh the page and resubmit your request.',
            'dakoii' => 'Security validation failed. This may be due to an expired session. Please try your request again.',
            'default' => 'CSRF token validation failed. Please refresh the page and try again.'
        ];

        $session->setFlashdata('error', $errorMessages[$portal] ?? $errorMessages['default']);

        // Redirect based on configuration
        $security = config('Security');
        if ($security->redirect) {
            $redirectURL = $request->getHeaderLine('Referer') ?: base_url();
            return $response->redirect($redirectURL);
        }

        // Show custom error page
        return $this->showCSRFErrorPage($portal);
    }

    /**
     * Show portal-specific CSRF error page
     *
     * @param string $portal
     * @return ResponseInterface
     */
    protected function showCSRFErrorPage(string $portal): ResponseInterface
    {
        $response = Services::response();
        
        $errorViews = [
            'admin' => 'errors/csrf_error_admin',
            'monitor' => 'errors/csrf_error_monitor', 
            'dakoii' => 'errors/csrf_error_dakoii',
            'default' => 'errors/html/error_419'
        ];

        $viewFile = $errorViews[$portal] ?? $errorViews['default'];
        
        try {
            $content = view($viewFile, [
                'portal' => $portal,
                'message' => 'CSRF token validation failed',
                'action' => 'Please refresh the page and try again'
            ]);
        } catch (\Exception $e) {
            // Fallback to basic error message if view doesn't exist
            $content = $this->getBasicErrorPage($portal);
        }

        return $response->setStatusCode(419)
                       ->setBody($content);
    }

    /**
     * Log CSRF failure for audit trail
     *
     * @param RequestInterface $request
     * @return void
     */
    protected function logCSRFFailure(RequestInterface $request): void
    {
        try {
            // Check if AuditService exists
            if (class_exists('\App\Libraries\AuditService')) {
                $auditService = new \App\Libraries\AuditService();
                
                $auditData = [
                    'event_type' => 'csrf_validation_failed',
                    'request_url' => $request->getUri()->__toString(),
                    'request_method' => $request->getMethod(),
                    'ip_address' => $request->getIPAddress(),
                    'user_agent' => $request->getUserAgent()->__toString(),
                    'portal' => $this->detectPortal($request),
                    'timestamp' => date('Y-m-d H:i:s'),
                    'session_id' => session_id()
                ];

                $auditService->logSecurityEvent($auditData);
            }
        } catch (\Exception $e) {
            // Log to CodeIgniter's log if audit service fails
            log_message('error', 'CSRF validation failed: ' . $e->getMessage());
        }

        // Always log to CodeIgniter's security log
        log_message('security', 'CSRF token validation failed for ' . $request->getUri()->__toString() . 
                   ' from IP: ' . $request->getIPAddress());
    }

    /**
     * Get basic error page content as fallback
     *
     * @param string $portal
     * @return string
     */
    protected function getBasicErrorPage(string $portal): string
    {
        $portalNames = [
            'admin' => 'Admin Portal',
            'monitor' => 'Monitor Portal',
            'dakoii' => 'Dakoii Portal',
            'default' => 'PROMIS System'
        ];

        $portalName = $portalNames[$portal] ?? $portalNames['default'];

        return '<!DOCTYPE html>
<html>
<head>
    <title>Security Error - ' . $portalName . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; text-align: center; }
        .error-container { max-width: 600px; margin: 0 auto; }
        .error-code { font-size: 72px; color: #dc3545; margin-bottom: 20px; }
        .error-message { font-size: 24px; margin-bottom: 20px; }
        .error-description { color: #666; margin-bottom: 30px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">419</div>
        <div class="error-message">Security Token Expired</div>
        <div class="error-description">
            Your security token has expired. This is a security measure to protect against unauthorized requests.
        </div>
        <a href="javascript:history.back()" class="btn">Go Back</a>
        <a href="' . base_url() . '" class="btn">Home</a>
    </div>
</body>
</html>';
    }
}
