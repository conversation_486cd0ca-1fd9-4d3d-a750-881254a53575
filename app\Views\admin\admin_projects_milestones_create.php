<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-milestones" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-flag me-2"></i>
            Create New Milestone
        </h1>
        <p class="text-muted mb-0">
            Add a new milestone to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Milestone Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-flag me-2"></i>
            Milestone Information
        </h5>
    </div>

    <div class="card-body">
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <h6 class="alert-heading">
                    <i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:
                </h6>
                <ul class="mb-0">
                    <?php foreach ($errors as $field => $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">

                <!-- Left Column -->
                <div class="col-md-6">
                    <!-- Phase Selection -->
                    <div class="mb-4">
                        <label for="phase_id" class="form-label fw-semibold">
                            Phase <span class="text-danger">*</span>
                        </label>
                        <select id="phase_id" name="phase_id" class="form-select <?= isset($errors['phase_id']) ? 'is-invalid' : '' ?>" required>
                            <option value="">Select Phase</option>
                            <?php foreach ($phases as $phase): ?>
                                <option value="<?= $phase['id'] ?>" <?= old('phase_id') == $phase['id'] ? 'selected' : '' ?>>
                                    <?= esc($phase['title']) ?> (<?= esc($phase['phase_code']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['phase_id'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['phase_id']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Select the phase this milestone belongs to
                        </div>
                    </div>

                    <!-- Milestone Code -->
                    <div class="mb-4">
                        <label for="milestone_code" class="form-label fw-semibold">
                            Milestone Code <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="milestone_code" 
                               name="milestone_code" 
                               class="form-control <?= isset($errors['milestone_code']) ? 'is-invalid' : '' ?>"
                               value="<?= old('milestone_code') ?>"
                               placeholder="e.g., M001, DESIGN, REVIEW"
                               required>
                        <?php if (isset($errors['milestone_code'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['milestone_code']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Unique identifier for this milestone (max 20 characters)
                        </div>
                    </div>

                    <!-- Milestone Title -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            Milestone Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-control <?= isset($errors['title']) ? 'is-invalid' : '' ?>"
                               value="<?= old('title') ?>"
                               placeholder="e.g., Design Approval, Site Survey Complete"
                               required>
                        <?php if (isset($errors['title'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['title']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Descriptive name for this milestone (max 200 characters)
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                    <!-- Target Date -->
                    <div class="mb-4">
                        <label for="target_date" class="form-label fw-semibold">
                            Target Date
                        </label>
                        <input type="date" 
                               id="target_date" 
                               name="target_date" 
                               class="form-control <?= isset($errors['target_date']) ? 'is-invalid' : '' ?>"
                               value="<?= old('target_date') ?>">
                        <?php if (isset($errors['target_date'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['target_date']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            When this milestone should be completed
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="form-label fw-semibold">
                            Status <span class="text-danger">*</span>
                        </label>
                        <select id="status" 
                                name="status" 
                                class="form-select <?= isset($errors['status']) ? 'is-invalid' : '' ?>"
                                required>
                            <option value="">Select Status</option>
                            <option value="pending" <?= old('status') === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="in-progress" <?= old('status') === 'in-progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="completed" <?= old('status') === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="approved" <?= old('status') === 'approved' ? 'selected' : '' ?>>Approved</option>
                        </select>
                        <?php if (isset($errors['status'])): ?>
                            <div class="invalid-feedback"><?= esc($errors['status']) ?></div>
                        <?php endif; ?>
                        <div class="form-text">
                            Current status of this milestone
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label for="description" class="form-label fw-semibold">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>"
                          rows="4"
                          placeholder="Detailed description of this milestone, deliverables, and acceptance criteria..."><?= old('description') ?></textarea>
                <?php if (isset($errors['description'])): ?>
                    <div class="invalid-feedback"><?= esc($errors['description']) ?></div>
                <?php endif; ?>
                <div class="form-text">
                    Optional detailed description of the milestone
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-check-circle me-2"></i>
                    Create Milestone
                </button>
            </div>
        </form>
    </div>
</div>



<?= $this->endSection() ?>
