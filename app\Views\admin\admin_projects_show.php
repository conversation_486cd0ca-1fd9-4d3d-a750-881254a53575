<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Projects
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-pencil me-2"></i>
    Edit Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-folder-open me-2"></i>
            Project Profile
        </h1>
        <p class="text-muted mb-0">
            Complete information and phase management for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            <i class="bi bi-lightning me-2"></i>Quick Actions
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <!-- Edit Project -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>" class="btn btn-outline-primary w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-pencil fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Edit Project</span>
                        <small class="d-block text-muted">Update information</small>
                    </div>
                </a>
            </div>

            <!-- Manage Budget -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-outline-success w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-currency-dollar fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Budget</span>
                        <small class="d-block text-muted">Budget & tracking</small>
                    </div>
                </a>
            </div>

            <!-- Manage Expenses -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-outline-info w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-credit-card fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Expenses</span>
                        <small class="d-block text-muted">Track payments</small>
                    </div>
                </a>
            </div>

            <!-- Manage Outcomes -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-outline-warning w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-target fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Outcomes</span>
                        <small class="d-block text-muted">Success metrics</small>
                    </div>
                </a>
            </div>

            <!-- Manage Issues -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-exclamation-circle fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Issues</span>
                        <small class="d-block text-muted">Impact assessment</small>
                    </div>
                </a>
            </div>

            <!-- Impact Indicators -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-outline-primary w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-bar-chart fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Impact Indicators</span>
                        <small class="d-block text-muted">M&E framework</small>
                    </div>
                </a>
            </div>

            <!-- Manage Risks -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-outline-danger w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Risks</span>
                        <small class="d-block text-muted">Risk management</small>
                    </div>
                </a>
            </div>

            <!-- Manage Officers -->
            <div class="col-md-6 col-lg-3">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex align-items-center justify-content-center text-decoration-none">
                    <div class="text-center">
                        <i class="bi bi-people fs-4 mb-2 d-block"></i>
                        <span class="fw-semibold">Manage Officers</span>
                        <small class="d-block text-muted">Project team</small>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Project Overview Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center gap-3">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold promis-btn-gradient"
                 style="width: 48px; height: 48px;">
                <i class="bi bi-folder"></i>
            </div>
            <div class="flex-grow-1">
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($project['title']) ?>
                </h5>
                <div class="text-muted small">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
            <div>
                <?php
                $statusClasses = [
                    'planning' => 'bg-secondary',
                    'active' => 'bg-success',
                    'on-hold' => 'bg-warning',
                    'completed' => 'bg-primary',
                    'cancelled' => 'bg-danger'
                ];
                $statusClass = $statusClasses[$project['status']] ?? 'bg-secondary';
                ?>
                <span class="badge <?= $statusClass ?> text-uppercase px-3 py-2">
                    <?= esc($project['status']) ?>
                </span>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Basic Information Grid -->
        <div class="row g-4 mb-4">

            <!-- Left Column -->
            <div class="col-md-6">
                <!-- Project Goal -->
                <?php if ($project['goal']): ?>
                    <div class="mb-4">
                        <h6 class="fw-semibold text-primary mb-2 text-uppercase">
                            Project Goal
                        </h6>
                        <p class="text-secondary mb-0">
                            <?= esc($project['goal']) ?>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Other Project IDs -->
                <?php if ($project['other_project_ids']): ?>
                    <div class="mb-4">
                        <h6 class="fw-semibold text-primary mb-2 text-uppercase">
                            External References
                        </h6>
                        <p class="text-secondary font-monospace small mb-0">
                            <?= esc($project['other_project_ids']) ?>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Timeline Information -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Timeline
                    </h6>
                    <div class="row g-2 small">
                        <?php if ($project['initiation_date']): ?>
                            <div class="col-6">
                                <div class="text-muted">Initiated:</div>
                                <div class="text-secondary"><?= date('M j, Y', strtotime($project['initiation_date'])) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['start_date']): ?>
                            <div class="col-6">
                                <div class="text-muted">Started:</div>
                                <div class="text-secondary"><?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['end_date']): ?>
                            <div class="col-6">
                                <div class="text-muted">End Date:</div>
                                <div class="text-secondary"><?= date('M j, Y', strtotime($project['end_date'])) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['baseline_year']): ?>
                            <div class="col-6">
                                <div class="text-muted">Baseline Year:</div>
                                <div class="text-secondary"><?= esc($project['baseline_year']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['target_year']): ?>
                            <div class="col-6">
                                <div class="text-muted">Target Year:</div>
                                <div class="text-secondary"><?= esc($project['target_year']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if (!$project['initiation_date'] && !$project['start_date'] && !$project['end_date'] && !$project['baseline_year'] && !$project['target_year']): ?>
                            <div class="col-12">
                                <span class="text-muted fst-italic">No timeline information available</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-md-6">
                <!-- Location Information -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Location
                    </h6>
                    <div class="row g-2 small">
                        <?php if ($project['address_line']): ?>
                            <div class="col-12">
                                <div class="text-muted">Address:</div>
                                <div class="text-secondary"><?= esc($project['address_line']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['country_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">Country:</div>
                                <div class="text-secondary"><?= esc($project['country_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['province_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">Province:</div>
                                <div class="text-secondary"><?= esc($project['province_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['district_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">District:</div>
                                <div class="text-secondary"><?= esc($project['district_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['llg_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">LLG:</div>
                                <div class="text-secondary"><?= esc($project['llg_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['ward_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">Ward:</div>
                                <div class="text-secondary"><?= esc($project['ward_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['village_name']): ?>
                            <div class="col-6">
                                <div class="text-muted">Village:</div>
                                <div class="text-secondary"><?= esc($project['village_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['gps_point']): ?>
                            <div class="col-12">
                                <div class="text-muted">GPS:</div>
                                <div class="text-secondary font-monospace"><?= esc($project['gps_point']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if (!$project['address_line'] && !$project['country_name'] && !$project['province_name'] && !$project['district_name'] && !$project['llg_name'] && !$project['ward_name'] && !$project['village_name'] && !$project['gps_point']): ?>
                            <div class="col-12">
                                <span class="text-muted fst-italic">No location information available</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Project Metadata -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Project Metadata
                    </h6>
                    <div class="row g-2 small">
                        <div class="col-12">
                            <div class="text-muted">Created:</div>
                            <div class="text-secondary"><?= date('M j, Y \a\t g:i A', strtotime($project['created_at'])) ?></div>
                        </div>

                        <?php if ($project['creator_name']): ?>
                            <div class="col-12">
                                <div class="text-muted">Created by:</div>
                                <div class="text-secondary"><?= esc($project['creator_name']) ?></div>
                            </div>
                        <?php endif; ?>

                        <?php if ($project['updated_at'] && $project['updated_at'] !== $project['created_at']): ?>
                            <div class="col-12">
                                <div class="text-muted">Last Updated:</div>
                                <div class="text-secondary"><?= date('M j, Y \a\t g:i A', strtotime($project['updated_at'])) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Description -->
        <?php if ($project['description']): ?>
            <div class="mb-4">
                <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                    Project Description
                </h6>
                <div class="bg-light rounded p-3 text-secondary">
                    <?= nl2br(esc($project['description'])) ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Status Notes -->
        <?php if ($project['status_notes']): ?>
            <div class="mb-4">
                <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                    Status Notes
                </h6>
                <div class="alert alert-info">
                    <?= nl2br(esc($project['status_notes'])) ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Phases -->
<a id="project-phases"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                <span class="me-2">📋</span>
                Project Phases
            </h5>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                <i class="bi bi-plus-circle me-1"></i>Add New Phase
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php if (!empty($phases)): ?>
            <!-- Phase Timeline using Bootstrap 5 Components -->
            <div class="row g-4">
                <?php foreach ($phases as $index => $phase): ?>
                    <div class="col-12">
                        <div class="card border-start border-4 <?= $phase['status'] === 'active' ? 'border-primary' : 'border-secondary' ?> shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start justify-content-between">
                                    <!-- Phase Info -->
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center gap-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3 <?= $phase['status'] === 'active' ? 'bg-primary' : 'bg-secondary' ?>" style="width: 2.5rem; height: 2.5rem;">
                                                    <i class="bi bi-diagram-3 text-white"></i>
                                                </div>
                                                <h5 class="text-primary mb-0 fw-semibold">
                                                    <?= esc($phase['title']) ?>
                                                </h5>
                                            </div>
                                            <span class="badge <?= $phase['status'] === 'active' ? 'bg-primary' : 'bg-secondary' ?> text-uppercase">
                                                <?= esc($phase['status']) ?>
                                            </span>
                                            <span class="badge bg-light text-dark border">
                                                Order #<?= esc($phase['sort_order']) ?>
                                            </span>
                                        </div>

                                        <div class="row g-3 mb-3">
                                            <div class="col-sm-6 col-lg-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-hash text-muted me-2"></i>
                                                    <div>
                                                        <div class="small text-muted fw-medium">Code</div>
                                                        <div class="fw-semibold font-monospace text-dark"><?= esc($phase['phase_code']) ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6 col-lg-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-flag text-muted me-2"></i>
                                                    <div>
                                                        <div class="small text-muted fw-medium">Milestones</div>
                                                        <div class="fw-semibold">
                                                            <span class="badge bg-info"><?= esc($phase['milestone_count'] ?? 0) ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php if ($phase['start_date']): ?>
                                                <div class="col-sm-6 col-lg-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-event text-muted me-2"></i>
                                                        <div>
                                                            <div class="small text-muted fw-medium">Start Date</div>
                                                            <div class="fw-semibold text-dark"><?= date('M j, Y', strtotime($phase['start_date'])) ?></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($phase['end_date']): ?>
                                                <div class="col-sm-6 col-lg-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-check text-muted me-2"></i>
                                                        <div>
                                                            <div class="small text-muted fw-medium">End Date</div>
                                                            <div class="fw-semibold text-dark"><?= date('M j, Y', strtotime($phase['end_date'])) ?></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($phase['description']): ?>
                                            <div class="bg-light rounded-3 p-3">
                                                <p class="text-secondary mb-0 lh-base">
                                                    <?= esc($phase['description']) ?>
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Phase Actions -->
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="<?= base_url('admin/projects/' . $project['id'] . '/phases/' . $phase['id'] . '/edit') ?>">
                                                    <i class="bi bi-pencil me-2"></i>Edit Phase
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-danger" href="#" onclick="showDeletePhaseModal(<?= $phase['id'] ?>, '<?= esc($phase['title']) ?>')">
                                                    <i class="bi bi-trash me-2"></i>Delete Phase
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 5rem; height: 5rem;">
                        <i class="bi bi-diagram-3 text-muted" style="font-size: 2rem;"></i>
                    </div>
                </div>
                <h4 class="text-secondary mb-2">No Phases Yet</h4>
                <p class="text-muted mb-4">Start organizing your project by creating the first phase to track progress and milestones.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-plus-circle me-2"></i>Create First Phase
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Milestones -->
<a id="project-milestones"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                <span class="me-2">🎯</span>
                Project Milestones
            </h5>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                <i class="bi bi-plus-circle me-1"></i>Add New Milestone
            </a>
        </div>
    </div>

    <div class="card-body">
        <?php if (!empty($milestones)): ?>
            <?php
            $currentPhase = null;
            foreach ($milestones as $milestone):
                // Group milestones by phase
                if ($currentPhase !== $milestone['phase_title']):
                    if ($currentPhase !== null): ?>
                        </div></div> <!-- Close previous phase group -->
                    <?php endif; ?>
                    <div class="mb-4">
                        <div class="d-flex align-items-center gap-3 mb-3">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 2.5rem; height: 2.5rem;">
                                <i class="bi bi-diagram-3"></i>
                            </div>
                            <h5 class="text-primary fw-semibold mb-0">
                                <?= esc($milestone['phase_title']) ?> 
                                <span class="badge bg-light text-dark border ms-2"><?= esc($milestone['phase_code']) ?></span>
                            </h5>
                        </div>
                        <div class="row g-3">
                    <?php
                    $currentPhase = $milestone['phase_title'];
                endif;
            ?>

                    <div class="col-12">
                        <div class="card border-start border-4 <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'border-success' : ($milestone['status'] === 'in-progress' ? 'border-warning' : 'border-secondary') ?> shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start justify-content-between">
                                    <!-- Milestone Info -->
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center gap-3 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3 <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'bg-success' : ($milestone['status'] === 'in-progress' ? 'bg-warning' : 'bg-secondary') ?>" style="width: 2rem; height: 2rem;">
                                                    <i class="bi bi-flag text-white"></i>
                                                </div>
                                                <h6 class="text-primary mb-0 fw-semibold">
                                                    <?= esc($milestone['title']) ?>
                                                </h6>
                                            </div>
                                            <span class="badge <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'bg-success' : ($milestone['status'] === 'in-progress' ? 'bg-warning text-dark' : 'bg-secondary') ?> text-uppercase">
                                                <?= esc($milestone['status']) ?>
                                            </span>
                                        </div>

                                        <div class="row g-3 mb-3">
                                            <div class="col-sm-6 col-lg-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-hash text-muted me-2"></i>
                                                    <div>
                                                        <div class="small text-muted fw-medium">Code</div>
                                                        <div class="fw-semibold font-monospace text-dark"><?= esc($milestone['milestone_code']) ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6 col-lg-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-file-earmark-text text-muted me-2"></i>
                                                    <div>
                                                        <div class="small text-muted fw-medium">Evidence</div>
                                                        <div class="fw-semibold">
                                                            <span class="badge bg-info"><?= esc($milestone['evidence_count'] ?? 0) ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php if ($milestone['target_date']): ?>
                                                <div class="col-sm-6 col-lg-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-event text-muted me-2"></i>
                                                        <div>
                                                            <div class="small text-muted fw-medium">Target Date</div>
                                                            <div class="fw-semibold text-dark"><?= date('M j, Y', strtotime($milestone['target_date'])) ?></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($milestone['completion_date']): ?>
                                                <div class="col-sm-6 col-lg-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-check text-muted me-2"></i>
                                                        <div>
                                                            <div class="small text-muted fw-medium">Completed</div>
                                                            <div class="fw-semibold">
                                                                <span class="badge bg-success"><?= date('M j, Y', strtotime($milestone['completion_date'])) ?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($milestone['description']): ?>
                                            <div class="bg-light rounded-3 p-3">
                                                <p class="text-secondary mb-0 lh-base">
                                                    <?= esc($milestone['description']) ?>
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Milestone Actions -->
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment') ?>">
                                                    <i class="bi bi-clipboard-check me-2"></i>Assessment
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/edit') ?>">
                                                    <i class="bi bi-pencil me-2"></i>Edit Milestone
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-danger" href="#" onclick="showDeleteMilestoneModal(<?= $milestone['id'] ?>, '<?= esc($milestone['title']) ?>')">
                                                    <i class="bi bi-trash me-2"></i>Delete Milestone
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php endforeach; ?>
                <?php if ($currentPhase !== null): ?>
                    </div></div> <!-- Close last phase group -->
                <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 5rem; height: 5rem;">
                        <i class="bi bi-flag text-muted" style="font-size: 2rem;"></i>
                    </div>
                </div>
                <h4 class="text-secondary mb-2">No Milestones Yet</h4>
                <p class="text-muted mb-4">Start tracking progress by creating project milestones to measure achievements.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-plus-circle me-2"></i>Create First Milestone
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Files & Documentation -->
<?php if ($project['gps_kml_path'] || $project['evaluation_file']): ?>
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">
            📎 Files & Documentation
        </h5>
    </div>

    <div class="card-body">
        <div class="row g-4">

            <!-- GPS KML File -->
            <div class="col-md-6">
                <h6 class="text-primary fw-semibold mb-3 text-uppercase">
                    GPS KML File
                </h6>
                <?php if ($project['gps_kml_path']): ?>
                    <div class="card bg-light border-0">
                        <div class="card-body d-flex align-items-center">
                            <div class="bg-primary rounded d-flex align-items-center justify-content-center me-3" style="width: 2.5rem; height: 2.5rem;">
                                <span class="text-white">📍</span>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold text-primary mb-1">
                                    <?= basename($project['gps_kml_path']) ?>
                                </div>
                                <div class="small text-muted">
                                    KML/KMZ Geographic Data
                                </div>
                            </div>
                            <a href="<?= base_url($project['gps_kml_path']) ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download me-1"></i>Download
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card bg-light border-0">
                        <div class="card-body text-center text-muted fst-italic">
                            No GPS KML file uploaded
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Evaluation File -->
            <div class="col-md-6">
                <h6 class="text-primary fw-semibold mb-3 text-uppercase">
                    Evaluation Document
                </h6>
                <?php if ($project['evaluation_file']): ?>
                    <div class="card bg-light border-0">
                        <div class="card-body d-flex align-items-center">
                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center me-3" style="width: 2.5rem; height: 2.5rem;">
                                <span class="text-white">📄</span>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold text-primary mb-1">
                                    <?= basename($project['evaluation_file']) ?>
                                </div>
                                <div class="small text-muted">
                                    Project Evaluation Document
                                </div>
                            </div>
                            <a href="<?= base_url($project['evaluation_file']) ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download me-1"></i>Download
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card bg-light border-0">
                        <div class="card-body text-center text-muted fst-italic">
                            No evaluation document uploaded
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Project Budget -->
<a id="budget-items"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                💰 Project Budget
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Item
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Budget Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            $<?= number_format($budgetStats['total_planned'], 2) ?>
                        </div>
                        <div class="text-muted small">Total Budget</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= count($budgetItems) ?>
                        </div>
                        <div class="text-muted small">Active Items</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($budgetItems)): ?>
            <!-- Recent Budget Items -->
            <div class="row g-3">
                <?php
                $displayItems = array_slice($budgetItems, 0, 5); // Show only first 5 items
                foreach ($displayItems as $item): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-1">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            <?= esc($item['item_code']) ?>
                                        </h6>
                                        <span class="badge bg-secondary">
                                            $<?= number_format($item['amount_planned'], 2) ?>
                                        </span>
                                    </div>
                                    <p class="text-secondary mb-0 small">
                                        <?= esc($item['description']) ?>
                                    </p>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/' . $item['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($budgetItems) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($budgetItems) ?> Budget Items
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">💰</div>
                <h4 class="text-secondary mb-2">No Budget Items Yet</h4>
                <p class="text-muted mb-4">Start planning your project budget by adding budget items.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/budgets/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-currency-dollar me-2"></i>Add First Budget Item
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Outcomes -->
<a id="project-outcomes"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                🎯 Project Outcomes
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Outcome
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Outcomes Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            <?= $outcomeStats['total_outcomes'] ?>
                        </div>
                        <div class="text-muted small">Total Outcomes</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= number_format($outcomeStats['total_quantity'], 2) ?>
                        </div>
                        <div class="text-muted small">Total Quantity</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($outcomes)): ?>
            <!-- Recent Outcomes -->
            <div class="row g-3">
                <?php
                $displayOutcomes = array_slice($outcomes, 0, 5); // Show only first 5 outcomes
                foreach ($displayOutcomes as $outcome): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-1">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            <?= number_format($outcome['quantity'], 2) ?> <?= $outcome['unit'] ? esc($outcome['unit']) : '' ?>
                                        </h6>
                                        <?php if ($outcome['unit']): ?>
                                            <span class="badge bg-secondary">
                                                <?= esc($outcome['unit']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-secondary mb-0 small">
                                        <?= esc($outcome['outcome_text']) ?>
                                    </p>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/' . $outcome['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($outcomes) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($outcomes) ?> Project Outcomes
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">🎯</div>
                <h4 class="text-secondary mb-2">No Project Outcomes Yet</h4>
                <p class="text-muted mb-4">Define measurable deliverables to track project success metrics.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-target me-2"></i>Add First Outcome
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Issues Addressed -->
<a id="issues-addressed"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                🎯 Issues Addressed
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Issue
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Issues Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-4">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            <?= $issueStats['total_issues'] ?>
                        </div>
                        <div class="text-muted small">Total Issues</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= $issueStats['by_type']['direct'] ?>
                        </div>
                        <div class="text-muted small">Direct</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-info mb-1">
                            <?= $issueStats['by_type']['indirect'] ?>
                        </div>
                        <div class="text-muted small">Indirect</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($issues)): ?>
            <!-- Recent Issues -->
            <div class="row g-3">
                <?php
                $displayIssues = array_slice($issues, 0, 5); // Show only first 5 issues
                foreach ($displayIssues as $issue): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-2">
                                        <?php if ($issue['issue_type'] === 'direct'): ?>
                                            <span class="badge bg-secondary">
                                                🎯 Direct
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-info">
                                                🔄 Indirect
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-primary mb-0 small lh-base">
                                        <?= esc($issue['description']) ?>
                                    </p>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/' . $issue['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($issues) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($issues) ?> Issues Addressed
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">🎯</div>
                <h4 class="text-secondary mb-2">No Issues Addressed Yet</h4>
                <p class="text-muted mb-4">Document the problems and challenges this project addresses for impact assessment.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-target me-2"></i>Add First Issue
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Impact Indicators -->
<a id="impact-indicators"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                📊 Impact Indicators
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-graph-up me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Indicator
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Indicators Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            <?= $indicatorSummary['total_indicators'] ?>
                        </div>
                        <div class="text-muted small">Total Indicators</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-success mb-1">
                            <?= $indicatorSummary['indicators_with_actual'] ?>
                        </div>
                        <div class="text-muted small">With Actual Values</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-info mb-1">
                            <?= $indicatorSummary['targets_achieved'] + $indicatorSummary['targets_exceeded'] ?>
                        </div>
                        <div class="text-muted small">Targets Met</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= number_format($indicatorSummary['average_achievement_rate'], 1) ?>%
                        </div>
                        <div class="text-muted small">Avg Achievement</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($indicators)): ?>
            <!-- Recent Indicators -->
            <div class="row g-3">
                <?php
                $displayIndicators = array_slice($indicators, 0, 5); // Show only first 5 indicators
                foreach ($displayIndicators as $indicator): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-2">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            <?= esc($indicator['indicator_text']) ?>
                                        </h6>
                                        <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '' &&
                                                  $indicator['target_value'] !== null && $indicator['target_value'] !== '' &&
                                                  is_numeric($indicator['actual_value']) && is_numeric($indicator['target_value']) &&
                                                  $indicator['target_value'] > 0): ?>
                                            <?php
                                            $achievementRate = ((float)$indicator['actual_value'] / (float)$indicator['target_value']) * 100;
                                            $statusClass = $achievementRate >= 100 ? 'bg-success' : ($achievementRate >= 75 ? 'bg-warning' : 'bg-danger');
                                            ?>
                                            <span class="badge <?= $statusClass ?>">
                                                <?= number_format($achievementRate, 1) ?>%
                                            </span>
                                        <?php elseif ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '' &&
                                                      (!is_numeric($indicator['actual_value']) || !is_numeric($indicator['target_value']))): ?>
                                            <span class="badge bg-info">
                                                COMPLETED
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="row g-1 small">
                                        <?php if ($indicator['baseline_value'] !== null && $indicator['baseline_value'] !== ''): ?>
                                            <div class="col-md-4">
                                                <span class="text-muted fw-medium">Baseline:</span>
                                                <span class="text-secondary ms-1"><?= is_numeric($indicator['baseline_value']) ? number_format($indicator['baseline_value'], 2) : esc($indicator['baseline_value']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($indicator['target_value'] !== null && $indicator['target_value'] !== ''): ?>
                                            <div class="col-md-4">
                                                <span class="text-muted fw-medium">Target:</span>
                                                <span class="text-info ms-1"><?= is_numeric($indicator['target_value']) ? number_format($indicator['target_value'], 2) : esc($indicator['target_value']) ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== ''): ?>
                                            <div class="col-md-4">
                                                <span class="text-muted fw-medium">Actual:</span>
                                                <span class="text-success ms-1"><?= is_numeric($indicator['actual_value']) ? number_format($indicator['actual_value'], 2) : esc($indicator['actual_value']) ?></span>
                                            </div>
                                        <?php else: ?>
                                            <div class="col-md-4">
                                                <span class="text-muted fw-medium">Actual:</span>
                                                <span class="text-muted fst-italic ms-1">Pending</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/' . $indicator['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($indicators) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($indicators) ?> Impact Indicators
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">📊</div>
                <h4 class="text-secondary mb-2">No Impact Indicators Yet</h4>
                <p class="text-muted mb-4">Define measurable impact metrics for monitoring and evaluation framework.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-bar-chart me-2"></i>Add First Indicator
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Risks -->
<a id="project-risks"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                ⚠️ Project Risks
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Risk
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <?php if (!empty($risks)): ?>
            <div class="row g-3">
                <?php
                // Show only first 3 risks in project profile
                $displayRisks = array_slice($risks, 0, 3);
                foreach ($displayRisks as $risk):
                    // Calculate risk score
                    $riskScores = ['low' => 1, 'medium' => 2, 'high' => 3, 'critical' => 4];
                    $riskScore = $riskScores[$risk['risk_level']] ?? 1;
                ?>
                    <div class="col-12">
                        <div class="card bg-light border">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="fw-semibold text-primary mb-2">
                                            <?= esc(substr($risk['description'], 0, 100)) ?><?= strlen($risk['description']) > 100 ? '...' : '' ?>
                                        </div>
                                        <div class="d-flex gap-2 small">
                                            <span class="badge bg-<?= $risk['risk_type'] === 'witnessed' ? 'danger' : ($risk['risk_type'] === 'foreseen' ? 'warning' : 'info') ?>">
                                                <?= ucfirst($risk['risk_type']) ?>
                                            </span>
                                            <span class="badge bg-<?= $risk['risk_level'] === 'critical' ? 'danger' : ($risk['risk_level'] === 'high' ? 'warning' : ($risk['risk_level'] === 'medium' ? 'info' : 'secondary')) ?>">
                                                <?= ucfirst($risk['risk_level']) ?> Level
                                            </span>
                                            <span class="badge bg-<?= $riskScore >= 4 ? 'danger' : ($riskScore >= 3 ? 'warning' : ($riskScore >= 2 ? 'info' : 'secondary')) ?>">
                                                Score: <?= $riskScore ?>/4
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/' . $risk['id'] . '/edit') ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($risks) > 3): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($risks) ?> Risks
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">⚠️</div>
                <h4 class="text-secondary mb-2">No Risks Identified</h4>
                <p class="text-muted mb-4">Start managing project risks by identifying potential issues.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/risks/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-exclamation-triangle me-2"></i>Add First Risk
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Expenses -->
<a id="project-expenses"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                💳 Project Expenses
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Add Expense
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Expenses Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            $<?= number_format($expenseStats['total_amount'], 2) ?>
                        </div>
                        <div class="text-muted small">Total Expenses</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= $expenseStats['total_count'] ?>
                        </div>
                        <div class="text-muted small">Expense Records</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($expenses)): ?>
            <!-- Recent Expenses -->
            <div class="row g-3">
                <?php
                $displayExpenses = array_slice($expenses, 0, 5); // Show only first 5 expenses
                foreach ($displayExpenses as $expense): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-1">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            $<?= number_format($expense['amount_paid'], 2) ?>
                                        </h6>
                                        <span class="badge bg-primary">
                                            <?= date('M d, Y', strtotime($expense['paid_on'])) ?>
                                        </span>
                                        <?php if (!empty($expense['milestone_code'])): ?>
                                            <span class="badge bg-secondary">
                                                <?= esc($expense['milestone_code']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-secondary mb-0 small">
                                        <?= esc($expense['description']) ?>
                                    </p>
                                </div>
                                <div class="d-flex gap-2 ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id']) ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-eye me-1"></i>View
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/edit') ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-pencil me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($expenses) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($expenses) ?> Expense Records
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">💳</div>
                <h4 class="text-secondary mb-2">No Expenses Recorded</h4>
                <p class="text-muted mb-4">Start tracking project expenses by recording payments with supporting documentation.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-credit-card me-2"></i>Add First Expense
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Officers -->
<a id="project-officers"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                👥 Project Officers
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Assign Officer
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Officers Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            <?= $officerStats['total_officers'] ?>
                        </div>
                        <div class="text-muted small">Total Officers</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-success mb-1">
                            <?= $officerStats['by_role']['lead'] ?>
                        </div>
                        <div class="text-muted small">Lead Officers</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-secondary mb-1">
                            <?= $officerStats['by_role']['certifier'] ?>
                        </div>
                        <div class="text-muted small">Certifiers</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-info mb-1">
                            <?= $officerStats['by_role']['support'] ?>
                        </div>
                        <div class="text-muted small">Support Officers</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($officers)): ?>
            <!-- Recent Officers -->
            <div class="row g-3">
                <?php
                $displayOfficers = array_slice($officers, 0, 5); // Show only first 5 officers
                foreach ($displayOfficers as $officer): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-1">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            <?= esc($officer['name']) ?>
                                        </h6>
                                        <span class="badge bg-<?= $officer['role'] === 'lead' ? 'success' : ($officer['role'] === 'certifier' ? 'secondary' : 'info') ?> text-uppercase">
                                            <?php if ($officer['role'] === 'lead'): ?>
                                                👑 Lead
                                            <?php elseif ($officer['role'] === 'certifier'): ?>
                                                ✅ Certifier
                                            <?php else: ?>
                                                🤝 Support
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <p class="text-secondary mb-0 small">
                                        <?= esc($officer['username']) ?> • <?= esc($officer['email']) ?>
                                    </p>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-people me-1"></i>Manage
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($officers) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($officers) ?> Project Officers
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">👥</div>
                <h4 class="text-secondary mb-2">No Officers Assigned</h4>
                <p class="text-muted mb-4">Start building your project team by assigning officers with specific roles.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/officers/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-plus-circle me-2"></i>Assign First Officer
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Contractors -->
<a id="project-contractors"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                🏗️ Project Contractors
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-plus-circle me-1"></i>Assign Contractor
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Contractors Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-primary mb-1">
                            <?= $contractorStats['total_contractors'] ?>
                        </div>
                        <div class="text-muted small">Total Contractors</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-success mb-1">
                            <?= $contractorStats['by_client_flag']['positive'] ?>
                        </div>
                        <div class="text-muted small">Positive Rating</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-warning mb-1">
                            <?= $contractorStats['by_client_flag']['neutral'] ?>
                        </div>
                        <div class="text-muted small">Neutral Rating</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light border-0 text-center">
                    <div class="card-body">
                        <div class="h4 fw-bold text-danger mb-1">
                            <?= $contractorStats['by_client_flag']['negative'] ?>
                        </div>
                        <div class="text-muted small">Negative Rating</div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($contractors)): ?>
            <!-- Recent Contractors -->
            <div class="row g-3">
                <?php
                $displayContractors = array_slice($contractors, 0, 5); // Show only first 5 contractors
                foreach ($displayContractors as $contractor): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center gap-3 mb-1">
                                        <h6 class="text-primary mb-0 fw-semibold">
                                            <?= esc($contractor['contractor_name']) ?>
                                        </h6>
                                        <span class="badge bg-<?= $contractor['client_flag'] === 'positive' ? 'success' : ($contractor['client_flag'] === 'negative' ? 'danger' : 'warning') ?> text-uppercase">
                                            <?php if ($contractor['client_flag'] === 'positive'): ?>
                                                👍 Positive
                                            <?php elseif ($contractor['client_flag'] === 'negative'): ?>
                                                👎 Negative
                                            <?php else: ?>
                                                ➖ Neutral
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <p class="text-secondary mb-0 small">
                                        Code: <?= esc($contractor['contractor_code']) ?> • Joined: <?= date('M j, Y', strtotime($contractor['joined_at'])) ?>
                                    </p>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-building me-1"></i>Manage
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <?php if (count($contractors) > 5): ?>
                    <div class="col-12">
                        <div class="text-center pt-3">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-1"></i>View All <?= count($contractors) ?> Project Contractors
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">🏗️</div>
                <h4 class="text-secondary mb-2">No Contractors Assigned</h4>
                <p class="text-muted mb-4">Start building your project team by assigning contractors to handle project work.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-plus-circle me-2"></i>Assign First Contractor
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Project Events -->
<a id="project-events"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                📅 Project Events
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/events') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-calendar-plus me-1"></i>Add Event
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <?php
        // Get recent project events (this would need to be added to the controller)
        // For now, we'll show a placeholder
        ?>
        <div class="text-center py-5">
            <div class="display-1 mb-3">📅</div>
            <h4 class="text-secondary mb-2">Project Events</h4>
            <p class="text-muted mb-4">Track project delays, incidents, and significant events that impact project progress.</p>
            <div class="d-flex gap-3 justify-content-center">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/events') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-bar-chart me-2"></i>View All Events
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-calendar-plus me-2"></i>Add New Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Project Documents -->
<a id="project-documents"></a>
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center justify-content-between">
            <h5 class="mb-0">
                📁 Project Documents
            </h5>
            <div class="d-flex gap-2">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-bar-chart me-1"></i>View All
                </a>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary btn-sm promis-btn-gradient">
                    <i class="bi bi-folder-plus me-1"></i>Upload Document
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <?php
        // Get recent documents for display (limit to 5)
        $documentModel = new \App\Models\ProjectDocumentModel();
        $recentDocuments = $documentModel->getByProject($project['id']);
        $recentDocuments = array_slice($recentDocuments, 0, 5);
        $documentStats = $documentModel->getDocumentStatistics($project['id']);
        ?>

        <?php if (!empty($recentDocuments)): ?>
            <!-- Document Statistics -->
            <div class="row g-3 mb-4">
                <div class="col-md-6">
                    <div class="card bg-light border-0 text-center">
                        <div class="card-body">
                            <div class="h4 fw-bold text-primary mb-1"><?= $documentStats['total_documents'] ?? 0 ?></div>
                            <div class="text-muted small text-uppercase">Total Documents</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light border-0 text-center">
                        <div class="card-body">
                            <div class="h4 fw-bold text-info mb-1"><?= $documentStats['recent_uploads'] ?? 0 ?></div>
                            <div class="text-muted small text-uppercase">Recent Uploads</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Documents List -->
            <div class="row g-3">
                <?php foreach ($recentDocuments as $document): ?>
                    <div class="col-12">
                        <div class="card bg-light border-0">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded d-flex align-items-center justify-content-center me-3" style="width: 2rem; height: 2rem;">
                                        <span class="text-white small">📄</span>
                                    </div>
                                    <div>
                                        <div class="fw-medium mb-1">
                                            <?= !empty($document['description']) ? esc($document['description']) : 'Document' ?>
                                        </div>
                                        <div class="small text-muted">
                                            <?= esc(basename($document['doc_path'])) ?> • v<?= $document['version_no'] ?? 1 ?> •
                                            <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="ms-3">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-download me-1"></i>Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 mb-3">📁</div>
                <h4 class="text-secondary mb-2">No Documents Uploaded</h4>
                <p class="text-muted mb-4">Start organizing project documents by uploading your first file.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-folder-plus me-2"></i>Upload First Document
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>



<!-- Delete Milestone Confirmation Modal -->
<div class="modal fade" id="deleteMilestoneModal" tabindex="-1" aria-labelledby="deleteMilestoneModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteMilestoneModalLabel">Delete Milestone</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">Are you sure you want to delete the milestone <strong id="milestoneNameToDelete"></strong>?</p>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    This action cannot be undone. All milestone data will be permanently removed.
                </div>
                <form id="deleteMilestoneForm" method="post" action="" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteMilestone()">Delete Milestone</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Phase Confirmation Modal -->
<div class="modal fade" id="deletePhaseModal" tabindex="-1" aria-labelledby="deletePhaseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePhaseModalLabel">Delete Phase</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">Are you sure you want to delete the phase <strong id="phaseNameToDelete"></strong>?</p>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    This action cannot be undone. All phase data will be permanently removed.
                </div>
                <form id="deletePhaseForm" method="post" action="" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeletePhase()">Delete Phase</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Project Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Project</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">Are you sure you want to delete the project <strong><?= esc($project['title']) ?></strong>?</p>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    This action cannot be undone. All project data will be permanently removed.
                </div>
                <form id="deleteForm" method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/delete') ?>" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Project</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Smooth scrolling for anchor navigation */
html {
    scroll-behavior: smooth;
}

/* Offset for fixed header if any */
a[id] {
    scroll-margin-top: 20px;
}

/* Quick action button hover effects */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Timeline Component Styles */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e9ecef 0%, #dee2e6 50%, #e9ecef 100%);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.5rem;
    z-index: 2;
}

.timeline-marker-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    margin-left: 1rem;
}

/* Milestone Progress Component */
.milestone-progress {
    position: relative;
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.milestone-progress::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    border-radius: 0.25rem 0 0 0.25rem;
}

.milestone-progress.status-pending::before {
    background: #6c757d;
}

.milestone-progress.status-in-progress::before {
    background: #ffc107;
}

.milestone-progress.status-completed::before,
.milestone-progress.status-approved::before {
    background: #198754;
}

/* Responsive timeline adjustments */
@media (max-width: 768px) {
    .timeline {
        padding-left: 1.5rem;
    }
    
    .timeline::before {
        left: 0.75rem;
    }
    
    .timeline-marker {
        left: -1.5rem;
    }
    
    .timeline-marker-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-width: 2px;
    }
    
    .timeline-content {
        margin-left: 0.5rem;
    }
}
</style>

<script>
// Bootstrap 5 Modal instances
let deleteMilestoneModal, deletePhaseModal, deleteModal;

// Initialize modals when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    deleteMilestoneModal = new bootstrap.Modal(document.getElementById('deleteMilestoneModal'));
    deletePhaseModal = new bootstrap.Modal(document.getElementById('deletePhaseModal'));
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
});

// Milestone delete modal functions
function showDeleteMilestoneModal(milestoneId, milestoneName) {
    document.getElementById('milestoneNameToDelete').textContent = milestoneName;
    document.getElementById('deleteMilestoneForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/milestones/') ?>' + milestoneId + '/delete';
    deleteMilestoneModal.show();
}

function confirmDeleteMilestone() {
    document.getElementById('deleteMilestoneForm').submit();
}

// Phase delete modal functions
function showDeletePhaseModal(phaseId, phaseName) {
    document.getElementById('phaseNameToDelete').textContent = phaseName;
    document.getElementById('deletePhaseForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/phases/') ?>' + phaseId + '/delete';
    deletePhaseModal.show();
}

function confirmDeletePhase() {
    document.getElementById('deletePhaseForm').submit();
}

// Project delete modal functions
function showDeleteModal() {
    deleteModal.show();
}

function confirmDelete() {
    document.getElementById('deleteForm').submit();
}
</script>

<?= $this->endSection() ?>
