<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-folder-plus me-2"></i>
    Create New Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-kanban me-2"></i>
            Project Management
        </h1>
        <p class="text-muted mb-0">
            Manage projects for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            Filters & Search
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?= base_url('admin/projects') ?>" class="row g-3 align-items-end">

            <!-- Search -->
            <div class="col-md-4">
                <label for="search" class="form-label fw-semibold">Search Projects</label>
                <input type="text" id="search" name="search" class="form-control"
                       placeholder="Search projects..." value="<?= esc($filters['search']) ?>">
            </div>

            <!-- Status Filter -->
            <div class="col-md-3">
                <label for="status" class="form-label fw-semibold">Status</label>
                <select id="status" name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="planning" <?= ($filters['status'] === 'planning') ? 'selected' : '' ?>>Planning</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="on-hold" <?= ($filters['status'] === 'on-hold') ? 'selected' : '' ?>>On Hold</option>
                    <option value="completed" <?= ($filters['status'] === 'completed') ? 'selected' : '' ?>>Completed</option>
                    <option value="cancelled" <?= ($filters['status'] === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                </select>
            </div>

            <!-- Organization Context (Auto-filtered) -->
            <div class="col-md-3">
                <label class="form-label fw-semibold">Organization</label>
                <div class="d-flex align-items-center gap-2 bg-light border border-primary rounded p-2">
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 30px; height: 30px; background: var(--promis-gradient-primary);">
                        <i class="bi bi-building"></i>
                    </div>
                    <div>
                        <div class="fw-semibold text-primary small">
                            <?= esc($admin_organization_name) ?>
                        </div>
                        <div class="text-muted fs-6">
                            Your Organization
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="col-md-2">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary promis-btn-gradient">
                        <i class="bi bi-funnel me-2"></i>
                        Filter
                    </button>
                    <a href="<?= base_url('admin/projects') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        Clear
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Projects Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-kanban me-2"></i>
            Projects (<?= count($projects) ?> found)
        </h5>
    </div>

    <?php if (!empty($projects)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Project</th>
                        <th>Status</th>
                        <th>Location</th>
                        <th>Timeline</th>
                        <th>Created</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($projects as $project): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-3">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                         style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                                        <i class="bi bi-folder"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-primary mb-1">
                                            <?= esc($project['title']) ?>
                                        </div>
                                        <div class="text-muted small">
                                            <?= esc($project['pro_code']) ?>
                                            <?php if ($project['goal']): ?>
                                                • <?= esc(substr($project['goal'], 0, 50)) ?><?= strlen($project['goal']) > 50 ? '...' : '' ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'planning' => 'bg-secondary',
                                    'active' => 'bg-success',
                                    'on-hold' => 'bg-warning',
                                    'completed' => 'bg-primary',
                                    'cancelled' => 'bg-danger'
                                ];
                                $statusClass = $statusClasses[$project['status']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?= $statusClass ?> text-uppercase">
                                    <?= esc($project['status']) ?>
                                </span>
                            </td>
                            <td>
                                <div class="fs-6 text-muted">
                                    <?php if ($project['country_name'] || $project['province_name'] || $project['district_name'] || $project['llg_name']): ?>
                                        <?php
                                        $locationParts = array_filter([
                                            $project['llg_name'],
                                            $project['district_name'],
                                            $project['province_name'],
                                            $project['country_name']
                                        ]);
                                        echo esc(implode(', ', $locationParts));
                                        ?>
                                    <?php else: ?>
                                        <span class="text-muted">No location set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="fs-6 text-muted">
                                    <?php if ($project['start_date'] && $project['end_date']): ?>
                                        <div><?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                        <div class="fs-6 text-muted">to <?= date('M j, Y', strtotime($project['end_date'])) ?></div>
                                    <?php elseif ($project['start_date']): ?>
                                        <div>Started: <?= date('M j, Y', strtotime($project['start_date'])) ?></div>
                                    <?php elseif ($project['initiation_date']): ?>
                                        <div>Initiated: <?= date('M j, Y', strtotime($project['initiation_date'])) ?></div>
                                    <?php else: ?>
                                        <span class="text-muted">No timeline set</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="text-muted fs-6">
                                    <?= date('M j, Y', strtotime($project['created_at'])) ?>
                                </span>
                                <?php if ($project['creator_name']): ?>
                                    <div class="fs-6 text-muted">
                                        by <?= esc($project['creator_name']) ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <div class="d-flex gap-2 justify-content-center">
                                    <a href="<?= base_url('admin/projects/' . $project['id']) ?>"
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/edit') ?>"
                                       class="btn btn-outline-secondary btn-sm" title="Edit Project">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button onclick="showDeleteModal(<?= $project['id'] ?>, '<?= esc($project['title']) ?>')"
                                            class="btn btn-outline-danger btn-sm" title="Delete Project">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="card-body text-center py-5">
            <div class="display-1 text-muted opacity-50 mb-4">
                <i class="bi bi-kanban"></i>
            </div>
            <h5 class="text-muted mb-3">No projects found</h5>
            <p>Try adjusting your filters or create a new project.</p>
            <a href="<?= base_url('admin/projects/create') ?>" class="btn btn-primary promis-btn-gradient">
                Create First Project
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    Delete Project
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">
                    Are you sure you want to delete the project <strong id="deleteProjectName"></strong>?
                </p>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    This action cannot be undone. All project data will be permanently removed.
                </div>
                
                <form id="deleteForm" method="post" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="bi bi-trash me-2"></i>
                    Delete Project
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentDeleteProjectId = null;

function showDeleteModal(projectId, projectName) {
    currentDeleteProjectId = projectId;
    document.getElementById('deleteProjectName').textContent = projectName;
    
    // Use Bootstrap 5 modal API
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function confirmDelete() {
    if (currentDeleteProjectId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/') ?>' + currentDeleteProjectId + '/delete';
        form.submit();
    }
}

// Reset currentDeleteProjectId when modal is hidden
document.getElementById('deleteModal').addEventListener('hidden.bs.modal', function () {
    currentDeleteProjectId = null;
});
</script>

<?= $this->endSection() ?>
