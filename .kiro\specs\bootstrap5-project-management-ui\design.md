# Design Document

## Overview

This design document outlines the comprehensive migration of the PROMIS Admin Project Management interface from custom CSS variables and inline styles to a fully Bootstrap 5-compliant implementation. The migration will maintain the existing functionality and visual appeal while improving maintainability, consistency, and accessibility.

The project management interface consists of multiple interconnected views including project listing, project profile/detail, project creation/editing, and various sub-modules (budgets, phases, milestones, contractors, officers, expenses, outcomes, indicators, risks, and issues).

## Architecture

### Current State Analysis

The current implementation uses a hybrid approach:
- **Bootstrap 5 Framework**: Already loaded via CDN in the admin template
- **Custom CSS Variables**: Defined in the admin template (e.g., `--promis-gradient-primary`, `--text-primary`, `--spacing-xl`)
- **Mixed Styling Approach**: Some components use Bootstrap 5 classes while others use inline styles with CSS variables
- **Inconsistent Patterns**: Different views use different styling approaches

### Target Architecture

The target architecture will standardize on:
- **Bootstrap 5 Components**: All UI components will use native Bootstrap 5 classes
- **Bootstrap 5 Utilities**: Spacing, typography, colors, and layout will use Bootstrap utilities
- **Preserved Branding**: Custom CSS variables for brand-specific elements (gradients, colors) will be maintained
- **Consistent Patterns**: All project management views will follow the same styling patterns

## Components and Interfaces

### 1. Project List Interface (`admin_projects_list.php`)

**Current Issues:**
- Custom modal implementation using inline styles and CSS variables
- Mixed use of Bootstrap and custom styling
- Inconsistent spacing and typography

**Design Solution:**
```html
<!-- Replace custom modal with Bootstrap 5 Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteModalLabel">Delete Project</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to delete the project <strong id="deleteProjectName"></strong>?</p>
        <div class="alert alert-danger" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          This action cannot be undone. All project data will be permanently removed.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Project</button>
      </div>
    </div>
  </div>
</div>
```

**Key Changes:**
- Replace custom modal with Bootstrap 5 modal component
- Use Bootstrap 5 alert component for warning messages
- Standardize button classes and spacing
- Maintain existing table structure with Bootstrap 5 table classes

### 2. Project Profile Interface (`admin_projects_show.php`)

**Current Issues:**
- Multiple custom modals with inline styles
- Inconsistent card layouts
- Mixed styling approaches across sections

**Design Solution:**
```html
<!-- Standardized section cards -->
<div class="card mb-4">
  <div class="card-header bg-light">
    <div class="d-flex align-items-center justify-content-between">
      <h5 class="mb-0">
        <i class="bi bi-calendar-event me-2"></i>Project Phases
      </h5>
      <a href="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" 
         class="btn btn-primary btn-sm promis-btn-gradient">
        <i class="bi bi-plus-circle me-1"></i>Add Phase
      </a>
    </div>
  </div>
  <div class="card-body">
    <!-- Content with consistent Bootstrap 5 styling -->
  </div>
</div>
```

**Key Changes:**
- Standardize all section cards with consistent header/body structure
- Replace custom modals with Bootstrap 5 modal components
- Use Bootstrap 5 progress components for milestone tracking
- Implement Bootstrap 5 list groups for data display
- Maintain promis-btn-gradient for brand consistency

### 3. Project Forms (`admin_projects_create.php`, `admin_projects_edit.php`)

**Current Issues:**
- Inconsistent form validation styling
- Mixed use of custom and Bootstrap form classes
- Non-standard form layout patterns

**Design Solution:**
```html
<!-- Standardized form sections -->
<div class="card mb-4">
  <div class="card-header bg-light">
    <h5 class="card-title mb-0">
      <i class="bi bi-clipboard-data me-2"></i>Basic Information
    </h5>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-6">
        <label for="title" class="form-label fw-semibold">
          Project Title <span class="text-danger">*</span>
        </label>
        <input type="text" id="title" name="title" 
               class="form-control <?= isset($errors['title']) ? 'is-invalid' : '' ?>"
               value="<?= old('title') ?>" required>
        <?php if (isset($errors['title'])): ?>
          <div class="invalid-feedback"><?= $errors['title'] ?></div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>
```

**Key Changes:**
- Use Bootstrap 5 form validation classes (`is-invalid`, `is-valid`)
- Implement Bootstrap 5 feedback components (`invalid-feedback`, `valid-feedback`)
- Standardize form layout with Bootstrap 5 grid system
- Use consistent form control styling

### 4. Project Sub-modules

**Design Patterns for All Sub-modules:**

#### List Views Pattern:
```html
<div class="card">
  <div class="card-header bg-light">
    <div class="d-flex align-items-center justify-content-between">
      <h5 class="mb-0">
        <i class="bi bi-[icon] me-2"></i>[Module Name]
      </h5>
      <a href="[create-url]" class="btn btn-primary btn-sm promis-btn-gradient">
        <i class="bi bi-plus-circle me-1"></i>Add [Item]
      </a>
    </div>
  </div>
  <div class="card-body p-0">
    <div class="table-responsive">
      <table class="table table-hover mb-0">
        <!-- Table content -->
      </table>
    </div>
  </div>
</div>
```

#### Detail/Card Views Pattern:
```html
<div class="row g-4">
  <?php foreach ($items as $item): ?>
    <div class="col-lg-6">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex align-items-start justify-content-between mb-3">
            <div class="flex-grow-1">
              <h6 class="card-title mb-2">[Item Title]</h6>
              <p class="card-text text-muted small">[Item Description]</p>
            </div>
            <div class="dropdown">
              <button class="btn btn-outline-secondary btn-sm" type="button" 
                      data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="[edit-url]">
                  <i class="bi bi-pencil me-2"></i>Edit
                </a></li>
                <li><a class="dropdown-item text-danger" href="#" 
                       onclick="showDeleteModal([id], '[name]')">
                  <i class="bi bi-trash me-2"></i>Delete
                </a></li>
              </ul>
            </div>
          </div>
          <!-- Additional content -->
        </div>
      </div>
    </div>
  <?php endforeach; ?>
</div>
```

## Data Models

### CSS Class Mapping

**Current Custom Styles → Bootstrap 5 Equivalents:**

| Current Pattern | Bootstrap 5 Replacement |
|----------------|-------------------------|
| `style="display: flex; gap: var(--spacing-md)"` | `class="d-flex gap-3"` |
| `style="color: var(--text-primary)"` | `class="text-dark"` |
| `style="color: var(--text-secondary)"` | `class="text-muted"` |
| `style="padding: var(--spacing-xl)"` | `class="p-4"` |
| `style="margin-bottom: var(--spacing-lg)"` | `class="mb-3"` |
| `style="font-weight: 600"` | `class="fw-semibold"` |
| `style="font-size: 0.875rem"` | `class="fs-6"` |
| Custom modal implementation | Bootstrap 5 Modal component |
| Custom badge styling | Bootstrap 5 Badge component |
| Custom form validation | Bootstrap 5 Form validation |

### Preserved Custom Elements

**Elements to maintain for brand consistency:**
- `promis-btn-gradient` class for primary action buttons
- CSS custom properties for brand colors in template
- Custom sidebar and header styling (outside scope)
- Brand-specific color schemes and gradients

## Error Handling

### Form Validation Strategy

**Bootstrap 5 Validation Implementation:**
```php
<!-- Server-side validation display -->
<?php if (isset($errors) && !empty($errors)): ?>
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <h6 class="alert-heading">
      <i class="bi bi-exclamation-triangle me-2"></i>Please correct the following errors:
    </h6>
    <ul class="mb-0">
      <?php foreach ($errors as $field => $error): ?>
        <li><?= esc($error) ?></li>
      <?php endforeach; ?>
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
<?php endif; ?>

<!-- Individual field validation -->
<input type="text" name="field_name" 
       class="form-control <?= isset($errors['field_name']) ? 'is-invalid' : (old('field_name') ? 'is-valid' : '') ?>"
       value="<?= old('field_name') ?>">
<?php if (isset($errors['field_name'])): ?>
  <div class="invalid-feedback"><?= esc($errors['field_name']) ?></div>
<?php endif; ?>
```

### Modal Error Handling

**Standardized Modal Structure:**
```html
<div class="modal fade" id="errorModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title">
          <i class="bi bi-exclamation-triangle me-2"></i>Error
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-danger" role="alert">
          <div id="errorMessage">Error message will appear here</div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
```

## Testing Strategy

### Component Testing Approach

1. **Visual Regression Testing**
   - Compare before/after screenshots of each interface
   - Verify responsive behavior across device sizes
   - Ensure brand consistency is maintained

2. **Functional Testing**
   - Test all form submissions and validations
   - Verify modal interactions work correctly
   - Test all CRUD operations in sub-modules

3. **Accessibility Testing**
   - Verify proper ARIA labels and roles
   - Test keyboard navigation
   - Ensure proper focus management in modals

4. **Cross-browser Testing**
   - Test in Chrome, Firefox, Safari, Edge
   - Verify Bootstrap 5 compatibility
   - Test responsive behavior

### Implementation Phases

**Phase 1: Core Project Management**
- Project list interface
- Project profile/detail interface
- Project creation/edit forms

**Phase 2: Sub-modules (Part 1)**
- Budget management
- Phase and milestone management
- Officer and contractor management

**Phase 3: Sub-modules (Part 2)**
- Expense tracking
- Outcome management
- Risk and issue management
- Indicator management

**Phase 4: Testing and Refinement**
- Comprehensive testing across all interfaces
- Performance optimization
- Accessibility improvements
- Documentation updates

### Migration Strategy

1. **Preserve Functionality**: All existing functionality must remain intact
2. **Maintain Visual Consistency**: The overall look and feel should remain consistent
3. **Improve Maintainability**: Code should be cleaner and more maintainable
4. **Enhance Accessibility**: Improve accessibility through proper Bootstrap 5 components
5. **Responsive Design**: Ensure all interfaces work well on mobile devices

This design provides a comprehensive roadmap for migrating the entire Project Management interface to Bootstrap 5 while maintaining the existing functionality and visual appeal.