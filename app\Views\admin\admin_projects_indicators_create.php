<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Indicators</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="text-dark fs-2 fw-bold mb-2">
            Create Impact Indicator
        </h1>
        <p class="text-muted mb-0">
            Define a measurable impact metric for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Indicator Form -->
<div class="card">
    <div class="card-header">
        📊 Impact Indicator Information
    </div>

    <div class="card-body p-4">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="indicator-create-form">
            <?= csrf_field() ?>

            <!-- Indicator Description -->
            <div class="mb-4">
                <label for="indicator_text" class="form-label fw-semibold">
                    Indicator Description <span class="text-danger">*</span>
                </label>
                <textarea id="indicator_text"
                          name="indicator_text"
                          class="form-control border-danger border-2"
                          rows="4"
                          placeholder="e.g., Number of beneficiaries served, Percentage increase in literacy rate, Reduction in travel time..."
                          required><?= old('indicator_text') ?></textarea>
                <div class="form-text">
                    Clear description of what will be measured for impact assessment (max 255 characters)
                </div>
                <?php if (isset($errors['indicator_text'])): ?>
                    <div class="text-danger small mt-1">
                        <?= esc($errors['indicator_text']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="row g-4 mb-4">

                <!-- Left Column - Baseline Information -->
                <div class="col-md-6">
                    <h3 class="text-dark fs-5 fw-semibold mb-3 pb-2 border-bottom">
                        📈 Baseline Information
                    </h3>

                    <!-- Baseline Value -->
                    <div class="mb-4">
                        <label for="baseline_value" class="form-label fw-semibold">
                            Baseline Value
                        </label>
                        <input type="text"
                               id="baseline_value"
                               name="baseline_value"
                               class="form-control border-success"
                               value="<?= old('baseline_value') ?>"
                               placeholder="e.g., 0, 50%, Low, None"
                               maxlength="15">
                        <div class="form-text">
                            Starting value before project implementation - can be numbers or text (optional)
                        </div>
                        <?php if (isset($errors['baseline_value'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['baseline_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Baseline Date -->
                    <div class="mb-4">
                        <label for="baseline_date" class="form-label fw-semibold">
                            Baseline Date
                        </label>
                        <input type="date"
                               id="baseline_date"
                               name="baseline_date"
                               class="form-control border-success"
                               value="<?= old('baseline_date') ?>">
                        <div class="form-text">
                            Date when baseline value was measured (optional)
                        </div>
                        <?php if (isset($errors['baseline_date'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['baseline_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column - Target Information -->
                <div class="col-md-6">
                    <h3 class="text-dark fs-5 fw-semibold mb-3 pb-2 border-bottom">
                        🎯 Target Information
                    </h3>

                    <!-- Target Value -->
                    <div class="mb-4">
                        <label for="target_value" class="form-label fw-semibold">
                            Target Value
                        </label>
                        <input type="text"
                               id="target_value"
                               name="target_value"
                               class="form-control border-success border-2"
                               value="<?= old('target_value') ?>"
                               placeholder="e.g., 100, 80%, High, Complete"
                               maxlength="15">
                        <div class="form-text">
                            Expected value to be achieved by project completion - can be numbers or text (optional)
                        </div>
                        <?php if (isset($errors['target_value'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['target_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Target Date -->
                    <div class="mb-4">
                        <label for="target_date" class="form-label fw-semibold">
                            Target Date
                        </label>
                        <input type="date"
                               id="target_date"
                               name="target_date"
                               class="form-control border-success border-2"
                               value="<?= old('target_date') ?>">
                        <div class="form-text">
                            Expected date to achieve target value (optional)
                        </div>
                        <?php if (isset($errors['target_date'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['target_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Examples Section -->
            <div class="bg-light rounded-3 p-3 mb-4">
                <h4 class="text-dark fs-6 fw-semibold mb-2">
                    💡 Examples of Impact Indicators
                </h4>
                <div class="small text-secondary lh-base">
                    <div><strong>Access:</strong> "Number of people with improved access to clean water" (Baseline: 0, Target: 500)</div>
                    <div><strong>Quality:</strong> "Percentage improvement in education quality scores" (Baseline: 60%, Target: 85%)</div>
                    <div><strong>Time:</strong> "Reduction in travel time to health facility (minutes)" (Baseline: 120, Target: 30)</div>
                    <div><strong>Income:</strong> "Average household income increase (USD)" (Baseline: 200, Target: 350)</div>
                    <div><strong>Health:</strong> "Reduction in child mortality rate per 1000" (Baseline: 50, Target: 25)</div>
                </div>
            </div>

            <!-- Important Note -->
            <div class="alert alert-info mb-4">
                <h4 class="alert-heading fs-6 fw-semibold mb-2">
                    ℹ️ Important Note
                </h4>
                <p class="mb-0 small">
                    <strong>Admin Portal:</strong> Define baseline and target values for monitoring framework setup.<br>
                    <strong>M&E Team:</strong> Will add actual values and dates during project implementation through the monitoring portal.
                </p>
            </div>

            <!-- Form Actions -->
            <div class="d-flex gap-2 justify-content-end pt-3 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary">
                    ← Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    📊 Create Indicator
                </button>
            </div>
        </form>
    </div>
</div>


<?= $this->endSection() ?>
