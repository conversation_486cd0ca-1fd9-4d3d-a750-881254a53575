<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-person-plus me-2"></i>
    Create New User
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-people me-2"></i>
            User Management
        </h1>
        <p class="text-muted mb-0">
            Manage user accounts for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            Filters & Search
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?= base_url('admin/users') ?>" class="row g-3 align-items-end">

            <!-- Search -->
            <div class="col-md-4">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control"
                       placeholder="Search users..." value="<?= esc($filters['search']) ?>">
            </div>

            <!-- Role Filter -->
            <div class="col-md-2">
                <label class="form-label">Role</label>
                <select name="role" class="form-select">
                    <option value="">All Roles</option>
                    <option value="admin" <?= ($filters['role'] === 'admin') ? 'selected' : '' ?>>Admin</option>
                    <option value="moderator" <?= ($filters['role'] === 'moderator') ? 'selected' : '' ?>>Moderator</option>
                    <option value="editor" <?= ($filters['role'] === 'editor') ? 'selected' : '' ?>>Editor</option>
                    <option value="user" <?= ($filters['role'] === 'user') ? 'selected' : '' ?>>User</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div class="col-md-2">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($filters['status'] === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>

            <!-- Filter Buttons -->
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
            </div>
            <div class="col-md-2">
                <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-secondary w-100">
                    <i class="bi bi-arrow-clockwise me-1"></i>Clear
                </a>
            </div>
        </form>

        <!-- Organization Context -->
        <div class="mt-3 pt-3 border-top">
            <div class="d-flex align-items-center gap-2 bg-light border border-primary rounded p-2">
                <div class="rounded-circle d-flex align-items-center justify-content-center text-white bg-primary" style="width: 30px; height: 30px;">
                    <i class="bi bi-building"></i>
                </div>
                <div>
                    <div class="fw-semibold text-primary small">
                        <?= esc($admin_organization_name) ?>
                    </div>
                    <div class="text-muted small">
                        Your Organization
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>
            Users (<?= count($users) ?> found)
        </h5>
    </div>

    <?php if (!empty($users)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-3">
                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-semibold" style="width: 40px; height: 40px; font-size: 0.875rem;">
                                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-dark mb-1">
                                            <?= esc($user['name']) ?>
                                        </div>
                                        <div class="small text-muted">
                                            @<?= esc($user['username']) ?> • <?= esc($user['email']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $roleBadgeClasses = [
                                    'admin' => 'bg-danger',
                                    'moderator' => 'bg-warning',
                                    'editor' => 'bg-primary',
                                    'user' => 'bg-secondary'
                                ];
                                $roleBadgeClass = $roleBadgeClasses[$user['role']] ?? 'bg-secondary';
                                ?>
                                <div class="d-flex flex-column gap-1">
                                    <span class="badge <?= $roleBadgeClass ?> text-uppercase small">
                                        <?= esc($user['role']) ?>
                                    </span>

                                    <?php if ($user['is_project_officer'] || $user['is_evaluator'] || $user['is_supervisor']): ?>
                                        <div class="d-flex flex-wrap gap-1">
                                            <?php if ($user['is_project_officer']): ?>
                                                <span class="badge bg-info text-white" style="font-size: 0.625rem;">
                                                    📋 Project Officer
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($user['is_evaluator']): ?>
                                                <span class="badge bg-info text-white" style="font-size: 0.625rem;">
                                                    📊 Evaluator
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($user['is_supervisor']): ?>
                                                <span class="badge bg-info text-white" style="font-size: 0.625rem;">
                                                    👥 Supervisor
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if ($user['is_activated']): ?>
                                    <span class="badge bg-success">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        Inactive
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="text-secondary small">
                                    <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <?php if ($user['role'] !== 'admin'): ?>
                                        <a
                                            href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>"
                                            class="btn btn-primary btn-sm"
                                            title="Edit User"
                                        >
                                            <i class="bi bi-pencil me-1"></i>Edit
                                        </a>
                                    <?php endif; ?>
                                    <button
                                        type="button"
                                        class="btn btn-outline-secondary btn-sm"
                                        data-bs-toggle="modal"
                                        data-bs-target="#resetPasswordModal"
                                        data-user-id="<?= $user['id'] ?>"
                                        data-user-name="<?= esc($user['name']) ?>"
                                        title="Reset Password"
                                    >
                                        <i class="bi bi-key me-1"></i>Reset
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center p-5 text-muted">
            <i class="bi bi-people display-1 mb-3"></i>
            <h5 class="mb-3">No users found</h5>
            <p class="mb-4">Try adjusting your filters or create a new user.</p>
            <a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>Create First User
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">
                    <i class="bi bi-key me-2"></i>Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reset the password for <strong id="resetUserName"></strong>?</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    A new temporary password will be generated and displayed.
                </div>
                
                <form id="resetPasswordForm" method="post" class="d-none">
                    <?= csrf_field() ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmResetPassword()">
                    <i class="bi bi-key me-1"></i>Reset Password
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentResetUserId = null;

// Handle modal show event to set user data
document.getElementById('resetPasswordModal').addEventListener('show.bs.modal', function (event) {
    const button = event.relatedTarget;
    currentResetUserId = button.getAttribute('data-user-id');
    const userName = button.getAttribute('data-user-name');
    document.getElementById('resetUserName').textContent = userName;
});

// Handle modal hide event to clear data
document.getElementById('resetPasswordModal').addEventListener('hide.bs.modal', function (event) {
    currentResetUserId = null;
});

function confirmResetPassword() {
    if (currentResetUserId) {
        const form = document.getElementById('resetPasswordForm');
        form.action = '<?= base_url('admin/users/') ?>' + currentResetUserId + '/reset-password';
        form.submit();
    }
}
</script>

<?= $this->endSection() ?>
