<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    ← Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-person-gear me-2"></i>Edit User
        </h1>
        <p class="text-muted mb-0">
            Update user information for <strong><?= esc($user['name']) ?></strong>
        </p>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">User Information</h5>
    </div>
    
    <div class="card-body">
        <form action="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>" method="post" class="needs-validation" novalidate>

            <?= csrf_field() ?>
            
            <div class="row g-3 mb-3">
                
                <!-- Username (Read-only) -->
                <div class="col-md-6">
                    <label for="username" class="form-label">Username</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        value="<?= esc($user['username']) ?>"
                        readonly
                    >
                    <div class="form-text">Username cannot be changed</div>
                </div>

                <!-- Email -->
                <div class="col-md-6">
                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-control" 
                        value="<?= old('email', $user['email']) ?>"
                        placeholder="Enter email address"
                        required
                    >
                    <div class="form-text">Must be a valid email address</div>
                    <div class="invalid-feedback">Please provide a valid email address.</div>
                </div>
            </div>

            <div class="row g-3 mb-3">

                <!-- Full Name -->
                <div class="col-md-6">
                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-control"
                        value="<?= old('name', $user['name']) ?>"
                        placeholder="Enter full name"
                        required
                    >
                    <div class="invalid-feedback">Please provide a full name.</div>
                </div>

                <!-- Role -->
                <div class="col-md-6">
                    <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                    <select
                        id="role"
                        name="role"
                        class="form-select"
                        required
                    >
                        <option value="">Select Role</option>
                        <option value="moderator" <?= old('role', $user['role']) === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                        <option value="editor" <?= old('role', $user['role']) === 'editor' ? 'selected' : '' ?>>Editor</option>
                        <option value="user" <?= old('role', $user['role']) === 'user' ? 'selected' : '' ?>>User</option>
                    </select>
                    <div class="form-text">Administrator accounts cannot be edited</div>
                    <div class="invalid-feedback">Please select a role.</div>
                </div>
            </div>

            <!-- Organization (Read-only) -->
            <div class="mb-3">
                <label for="organization" class="form-label">Organization</label>
                <input
                    type="text"
                    id="organization"
                    name="organization"
                    class="form-control"
                    value="<?= esc($admin_organization_name) ?>"
                    readonly
                >
                <div class="form-text">Organization assignment cannot be changed</div>
            </div>

            <!-- Additional Permissions -->
            <div class="mb-4">
                <label class="form-label">Additional Permissions</label>
                <div class="card border-light bg-light">
                    <div class="card-body">
                        <p class="text-muted small mb-3">
                            Select additional permissions for this user. These are in addition to their role permissions.
                        </p>

                        <div class="row g-3">

                            <!-- Project Officer Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_project_officer"
                                        name="is_project_officer"
                                        value="1"
                                        <?= old('is_project_officer', $user['is_project_officer']) ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_project_officer">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    📋 Project Officer
                                                </div>
                                                <div class="text-muted small">
                                                    Can manage project activities and reports
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Evaluator Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_evaluator"
                                        name="is_evaluator"
                                        value="1"
                                        <?= old('is_evaluator', $user['is_evaluator']) ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_evaluator">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    📊 Evaluator
                                                </div>
                                                <div class="text-muted small">
                                                    Can evaluate projects and submit assessments
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Supervisor Permission -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input
                                        class="form-check-input"
                                        type="checkbox"
                                        id="is_supervisor"
                                        name="is_supervisor"
                                        value="1"
                                        <?= old('is_supervisor', $user['is_supervisor']) ? 'checked' : '' ?>
                                    >
                                    <label class="form-check-label w-100" for="is_supervisor">
                                        <div class="card h-100 border permission-card">
                                            <div class="card-body p-3">
                                                <div class="fw-semibold text-primary mb-1">
                                                    👥 Supervisor
                                                </div>
                                                <div class="text-muted small">
                                                    Can supervise teams and approve activities
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-text">
                    These permissions are in addition to the selected role
                </div>
            </div>

            <!-- Information Box -->
            <div class="alert alert-info border-primary bg-light my-4">
                <h6 class="alert-heading text-primary fw-semibold mb-2">
                    <i class="bi bi-info-circle me-2"></i>Edit Information
                </h6>
                <ul class="text-muted small mb-0 ps-3">
                    <li>Username and organization cannot be changed after creation</li>
                    <li>Email changes may require the user to verify their new email</li>
                    <li>Role changes take effect immediately</li>
                    <li>All changes are logged for security auditing</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-2 mt-4">
                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary order-2 order-sm-1">
                    Cancel
                </a>
                
                <button type="submit" class="btn btn-primary order-1 order-sm-2">
                    <i class="bi bi-save me-1"></i>Update User
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bootstrap 5 Custom Styling -->
<style>
/* Permission Card Interactions */
.permission-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.permission-card:hover {
    border-color: var(--bs-primary) !important;
    background-color: var(--bs-primary-bg-subtle);
}

.form-check-input:checked + .form-check-label .permission-card {
    border-color: var(--bs-primary) !important;
    background-color: var(--bs-primary-bg-subtle);
}

.form-check-input:checked + .form-check-label .permission-card .fw-semibold {
    color: var(--bs-primary) !important;
}

/* Cursor pointer for clickable labels */
.form-check-label {
    cursor: pointer;
}
</style>

<?= $this->endSection() ?>
