<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    ← Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            Create New User
        </h1>
        <p class="text-muted mb-0">
            Step 1 of 2: Account Details
        </p>
    </div>
</div>

<!-- Progress Indicator -->
<div class="mb-4">
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center fw-semibold" 
                 style="width: 32px; height: 32px; font-size: 0.875rem;">1</div>
            <span class="ms-2 fw-semibold text-primary">Account Details</span>
        </div>
        <div class="flex-fill mx-3">
            <hr class="border-2 opacity-25">
        </div>
        <div class="d-flex align-items-center">
            <div class="rounded-circle bg-light text-muted d-flex align-items-center justify-content-center fw-semibold" 
                 style="width: 32px; height: 32px; font-size: 0.875rem;">2</div>
            <span class="ms-2 text-muted">Roles & Permissions</span>
        </div>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">Account Information</h5>
    </div>
    
    <div class="card-body">
        <form action="<?= base_url('admin/users/create') ?>" method="post" class="needs-validation" novalidate>

            <?= csrf_field() ?>
            
            <div class="row g-3 mb-3">
                
                <!-- Username -->
                <div class="col-md-6">
                    <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        value="<?= old('username') ?>"
                        placeholder="Enter username"
                        required
                        autofocus
                    >
                    <div class="form-text">Must be unique and at least 3 characters</div>
                    <div class="invalid-feedback">Please provide a valid username.</div>
                </div>

                <!-- Email -->
                <div class="col-md-6">
                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-control" 
                        value="<?= old('email') ?>"
                        placeholder="Enter email address"
                        required
                    >
                    <div class="form-text">Must be a valid email address</div>
                    <div class="invalid-feedback">Please provide a valid email address.</div>
                </div>
            </div>

            <div class="row g-3 mb-3">
                
                <!-- Full Name -->
                <div class="col-md-6">
                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                    <input 
                        type="text" 
                        id="name" 
                        name="name" 
                        class="form-control" 
                        value="<?= old('name') ?>"
                        placeholder="Enter full name"
                        required
                    >
                    <div class="invalid-feedback">Please provide a full name.</div>
                </div>

                <!-- Phone -->
                <div class="col-md-6">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input 
                        type="tel" 
                        id="phone" 
                        name="phone" 
                        class="form-control" 
                        value="<?= old('phone') ?>"
                        placeholder="Enter phone number"
                    >
                    <div class="form-text">Optional - for contact purposes</div>
                </div>
            </div>

            <!-- Department -->
            <div class="mb-3">
                <label for="department" class="form-label">Department</label>
                <input 
                    type="text" 
                    id="department" 
                    name="department" 
                    class="form-control" 
                    value="<?= old('department') ?>"
                    placeholder="Enter department or division"
                >
                <div class="form-text">Optional - user's department or division</div>
            </div>

            <!-- Information Box -->
            <div class="alert alert-info border-primary bg-light my-4">
                <h6 class="alert-heading text-primary fw-semibold mb-2">
                    <i class="bi bi-clipboard-check me-2"></i>What happens next?
                </h6>
                <ul class="text-muted small mb-0 ps-3">
                    <li>A temporary password will be generated automatically</li>
                    <li>You'll configure roles and organization assignment in step 2</li>
                    <li>The user will be activated immediately upon creation</li>
                    <li>You can share the login credentials with the user</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-2 mt-4">
                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary order-2 order-sm-1">
                    Cancel
                </a>
                
                <button type="submit" class="btn btn-primary order-1 order-sm-2">
                    Continue to Step 2 →
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
